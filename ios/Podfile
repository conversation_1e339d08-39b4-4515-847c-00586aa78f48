 def node_require(script)
   require Pod::Executable.execute_command('node', ['-p',
     "require.resolve(
       '#{script}',
       {paths: [process.argv[1]]},
     )", __dir__]).strip
 end

 node_require('react-native/scripts/react_native_pods.rb')
 node_require('react-native-permissions/scripts/setup.rb')

platform :ios, "15.1"
prepare_react_native_project!

setup_permissions([
  'LocationAccuracy',
  'LocationAlways',
  'LocationWhenInUse',
  'MediaLibrary',
  'Notifications',
  'PhotoLibrary',
  'PhotoLibraryAddOnly',
])

use_frameworks! :linkage => :static
use_modular_headers!

target 'myCANx' do
  config = use_native_modules!

  pod 'Firebase/Core'
  pod 'Firebase/Auth'
  pod 'Firebase/Messaging'
  pod 'Firebase/Analytics'
  pod 'RNAppleAuthentication', :path => '../node_modules/@invertase/react-native-apple-authentication'
  pod 'BranchSDK'

  use_react_native!(
    :path => config[:reactNativePath],
    :app_path => "#{Pod::Config.instance.installation_root}/..",
    :fabric_enabled => true # Re-enable Fabric since we're using a compatible version
  )

  post_install do |installer|
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false
    )
  end
end
