import AsyncStorage from '@react-native-async-storage/async-storage';
import {getAnalytics, logEvent} from '@react-native-firebase/analytics';
import {GC_CUSTOMER_ID} from '../globals';
import {getApp} from '@react-native-firebase/app';

interface LogEventType {
  action: string;
  category?: string;
  label?: string;
  value?: number;
}

export const AnalyticService = {
  AddAnalyticsScreenView: async (pageTitle: string, pagePath: string) => {
    try {
      // Log a screen view event using the modular API
      const analytics = getAnalytics();
      logEvent(analytics, 'screen_view' as any, {
        screen_name: pageTitle,
        app_name: 'myCANx Customer',
      }).then(() => {
        console.log('AddAnalyticsScreenView: success !');
      });
    } catch (e) {
      console.error('AddAnalyticsScreenView: error =>:', e);
    }
  },
  AddAnalyticCustomer: async () => {
    try {
      const cust = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (cust) {
        // Set the user ID for analytics
        await getApp().analytics().setUserId(cust);
        console.log('Analytics user_id set:', cust);
      }
    } catch (e) {
      console.error('AddAnalyticCustomer: error =>:', e);
    }
  },
  AddAnalyticsEvent: async ({action, category, label, value}: LogEventType) => {
    try {
      await getApp().analytics().logEvent(action, {
        event_category: category,
        event_label: label,
        value: value,
      });
      console.log('Logged event:', action);
    } catch (e) {
      console.error('AddAnalyticsEvent: error =>:', e);
    }
  },
};
