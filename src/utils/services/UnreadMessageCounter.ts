import AsyncStorage from '@react-native-async-storage/async-storage';
import { onValue } from '@react-native-firebase/database';
import { GC_CUSTOMER_ID } from '../globals';
import { MessageService } from './MessageService';

type Callback = (count: number) => void;

let initialized = false;
let serviceUnsub: any = null;
let systemUnsub: any = null;
let serviceUnread = 0;
let systemUnread = 0;
let currentTotal = 0;
const subscribers = new Set<Callback>();

const notify = () => {
  currentTotal = serviceUnread + systemUnread;
  subscribers.forEach(cb => cb(currentTotal));
};

async function init() {
  if (initialized) return;
  initialized = true;
  try {
    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    const serviceQuery = await MessageService.getServiceMessages();
    serviceUnsub = onValue(serviceQuery, snap => {
      const data = snap.val() || {};
      let cnt = 0;
      Object.keys(data).forEach(key => {
        const msg = data[key] || {};
        if (!msg['customer-delete'] && !msg['customer-read']) {
          cnt += 1;
        }
      });
      serviceUnread = cnt;
      notify();
    });

    const systemRef = await MessageService.getSystemMessages();
    systemUnsub = onValue(systemRef, snap => {
      const data = snap.val() || {};
      let cnt = 0;
      Object.keys(data).forEach(key => {
        const m = data[key] || {};
        const deleted = customerId && m.delete && m.delete[customerId];
        const read = customerId && m.read && m.read[customerId];
        if (!deleted && !read) {
          cnt += 1;
        }
      });
      systemUnread = cnt;
      notify();
    });
  } catch {
    // ignore
  }
}

export function subscribeUnreadMessageCount(callback: Callback): () => void {
  subscribers.add(callback);
  callback(currentTotal);
  init();
  return () => {
    subscribers.delete(callback);
    if (subscribers.size === 0) {
      try { if (typeof serviceUnsub === 'function') serviceUnsub(); } catch {}
      try { if (typeof systemUnsub === 'function') systemUnsub(); } catch {}
      initialized = false;
      serviceUnsub = null;
      systemUnsub = null;
      serviceUnread = 0;
      systemUnread = 0;
      currentTotal = 0;
    }
  };
}

export function getCurrentUnreadMessageCount(): number {
  return currentTotal;
}


