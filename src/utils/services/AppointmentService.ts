import {
  getDatabase,
  ref,
  get,
  set,
  update,
  query,
  orderByKey,
  equalTo,
} from '@react-native-firebase/database';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_CUSTOMER_ID} from '../globals';
import Config from 'react-native-config';
import {GC_SEND_PUSHNOTIFICATION_URL} from '../../constants/constants';

export const AppointmentService = {
  cancelAppointment: async (
    appointmentId: string,
    mechanicId: string | object,
    requestId: string,
    notes: string,
  ) => {
    const db = getDatabase();
    const appointmentRef = ref(db, `appointment/${appointmentId}`);

    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) throw new Error('Customer ID not found');

      // Extract mechanicId string from object if needed
      let mechanicIdString: string | null = null;
      if (mechanicId) {
        if (typeof mechanicId === 'string') {
          mechanicIdString = mechanicId;
        } else if (typeof mechanicId === 'object' && mechanicId !== null) {
          // If it's an object, try to get the key/id
          mechanicIdString = Object.keys(mechanicId)[0] || null;
          console.log('Extracted mechanicId from object:', mechanicIdString);
        }
      }

      const db = getDatabase();
      // update customer appointment status
      await set(
        ref(db, `customer/${customerId}/appointments/${appointmentId}/status`),
        'cancelled',
      );

      // update mechanic appointment only if mechanicId provided
      if (mechanicIdString) {
        await set(
          ref(db, `mechanic/${mechanicIdString}/appointments/${appointmentId}/status`),
          'cancelled',
        );
      }
      // Ensure appointment node status is also updated
      await set(ref(db, `appointment/${appointmentId}/status`), 'cancelled');

      await update(ref(db, `appointment/${appointmentId}/cancelled-state`), {
        'cancelled-date': Math.floor(Date.now() / 1000),
        'cancelled-by': 'customer',
        'cancelled-id': customerId,
        notes: notes,
        images: '',
      });
      // update work-request status if provided
      if (requestId) {
        await set(ref(db, `work-request/${requestId}/status`), 'cancelled');
      }

      // Try to notify mechanic (best-effort). Try common token locations.
      if (mechanicIdString) {
        console.log('Attempting to send FCM notification for mechanic:', mechanicIdString);
        try {
          const mechNotifSnap = await get(ref(db, `mechanic/${mechanicIdString}/settings/notification`));
          const mechNotif = mechNotifSnap.val() || {};
          const token = mechNotif['fcm-token'] || mechNotif['fcmToken'] || mechNotif.token || '';
          if (token) {
            // Fetch customer name for personalized message
            const customerSnap = await get(ref(db, `customer/${customerId}`));
            const customerData = customerSnap.val() || {};
            const customerName = `${customerData['first-name'] || ''} ${customerData['last-name'] || ''}`.trim() || 'Customer';
            const title = 'Appointment cancelled';
            const body = `${customerName} has cancelled your appointment`;
            console.log('Sending FCM notification:', { title, body, token, type: 'appointment-cancel', params: requestId || '' });
            await AppointmentService.sendPUSHToUser(token, title, body, 'appointment', requestId || '');
            console.log('FCM notification sent successfully');
          } else {
            console.log('No FCM token found for mechanic:', mechanicIdString);
          }
        } catch (nfErr) {
          console.warn('Failed to send cancel notification to mechanic (non-fatal):', nfErr);
        }
      } else {
        console.log('No mechanicId provided, skipping FCM notification');
      }

      return true;
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      throw error;
    }
  },
  cancelworkRequest: async (requestId: string, mechanicId?: string) => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) throw new Error('Customer ID not found');

      const db = getDatabase();
      
      // Update customer work-requests status
      await set(
        ref(db, `customer/${customerId}/work-requests/${requestId}/status`),
        'cancelled',
      );
      
      // Update mechanic work-requests status only if mechanicId provided
      if (mechanicId) {
        await set(
          ref(db, `mechanic/${mechanicId}/work-requests/${requestId}/status`),
          'cancelled',
        );
      }
+     // Update central work-request node as well
+     await set(ref(db, `work-request/${requestId}/status`), 'cancelled');

      return true;
    } catch (error) {
      console.error('Error cancelling work request:', error);
      throw error;
    }
  },
  // Cancels a multiple-mechanic request (multiple-request / multiple-request-data)
  cancelMultipleWorkRequest: async (requestId: string) => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) throw new Error('Customer ID not found');

      const db = getDatabase();
      // update central multiple-request nodes (some apps use both keys)
      await set(ref(db, `multiple-request/${requestId}/status`), 'cancelled');
      await set(ref(db, `multiple-request-data/${requestId}/status`), 'cancelled');

      // also update work-request and customer/work-requests for compatibility
      await set(ref(db, `work-request/${requestId}/status`), 'cancelled');
      await set(
        ref(db, `customer/${customerId}/work-requests/${requestId}/status`),
        'cancelled',
      );

      return true;
    } catch (error) {
      console.error('Error cancelling multiple work request:', error);
      throw error;
    }
  },
  updateCancelCount: async () => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) throw new Error('Customer ID not found');

      const db = getDatabase();
      const countRef = ref(db, `customer/${customerId}/counts/cancelled-total`);
      const countSnapshot = await get(countRef);
      const currentCount = countSnapshot.val() || 0;

      await set(countRef, currentCount + 1);
    } catch (error) {
      console.error('Error updating cancel count:', error);
      throw error;
    }
  },

  updateLog: (workRequestId: string) => {
    const db = getDatabase();
    return ref(db, `log/${workRequestId}`);
  },

  refreshMultipleRequest: async (
    multipleRequestId: string,
    mechanicId: string,
  ) => {
    try {
      const db = getDatabase();
      const multipleRequestRef = ref(db, `multiple-request-data/${multipleRequestId}`);
      const snapshot = await get(multipleRequestRef);
      const multipleRequestData = snapshot.val();

      if (multipleRequestData) {
        // Update the status to indicate refresh or reset
        await update(multipleRequestRef, {
          status: 'pending', // or appropriate status
          lastUpdated: Math.floor(Date.now() / 1000),
        });
        console.log('Multiple request refreshed successfully');
      }
    } catch (error) {
      console.error('Error refreshing multiple request:', error);
      throw error;
    }
  },

  sendFCMNotification: async (
    title: string,
    body: string,
    token: string,
    type: string,
    params: string,
  ) => {
    try {
      const FCM_NOTIFICATION_KEY = Config.FCM_NOTIFICATION_KEY;
      if (!FCM_NOTIFICATION_KEY) {
        throw new Error('FCM_NOTIFICATION_KEY not configured');
      }
      console.log('FCM Key available:', !!FCM_NOTIFICATION_KEY);
      const bodyData = {
        notification: {
          title,
          body,
          sound: 'default',
          click_action: 'FCM_PLUGIN_ACTIVITY',
          icon: 'fcm_push_icon',
        },
        data: {
          type,
          params,
        },
        to: token,
        priority: 'high',
        restricted_package_name: '',
      };
      console.log('Sending FCM request to:', 'https://fcm.googleapis.com/fcm/send');
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
           Authorization: `key=${FCM_NOTIFICATION_KEY}`,
        },
        body: JSON.stringify(bodyData),
      });
      console.log('FCM response status:', response.status);
      if (!response.ok) {
        const errorText = await response.text();
        console.log('FCM error response:', errorText);
        throw new Error('FCM send failed');
      }
      const responseData = await response.json();
      console.log('FCM response data:', responseData);
    } catch (error) {
      console.error('Error sending FCM notification:', error);
      throw error;
    }
  },
  async sendPUSHToUser(
    token: string,
    title: string,
    body: string,
    type: string,
    params: string,
  ) {
    try {
      const msgBody = {
        token: token,
        title: title,
        body: body,
        type: type,
        params: params,
      };
      console.log('sendPUSHToUser payload:', JSON.stringify(msgBody, null, 2));
      const response = await fetch(GC_SEND_PUSHNOTIFICATION_URL, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(msgBody),
      });
      const responseText = await response.text();
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        // If not JSON, treat as error message
        return {error: true, status: response.status, message: responseText};
      }
      if (!data.success) {
        return {error: true, status: response.status, message: data.message || responseText};
      }
      return data;
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  },

  approveAppointment: async (requestId: string) => {
    try {
      const db = getDatabase();
      await set(ref(db, `work-request/${requestId}/isApproved`), true);

      // Fetch work-request data to get mechanicId
      const workRequestSnap = await get(ref(db, `work-request/${requestId}`));
      const workRequestData = workRequestSnap.val();
      const mechanicId = workRequestData?.mechanic;

      // Try to notify mechanic (best-effort)
      if (mechanicId) {
        console.log('Attempting to send FCM notification for mechanic:', mechanicId);
        try {
          const mechNotifSnap = await get(ref(db, `mechanic/${mechanicId}/settings/notification`));
          const mechNotif = mechNotifSnap.val() || {};
          const token = mechNotif['fcm-token'] || mechNotif['fcmToken'] || mechNotif.token || '';
          if (token) {
            // Fetch customer name for personalized message
            const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
            if (customerId) {
              const customerSnap = await get(ref(db, `customer/${customerId}`));
              const customerData = customerSnap.val() || {};
              const customerName = `${customerData['first-name'] || ''} ${customerData['last-name'] || ''}`.trim() || 'Customer';
              const title = 'Appointment approved';
              const body = `${customerName} has approved an appointment`;
              console.log('Sending FCM notification:', { title, body, token, type: 'appointment-approve', params: requestId || '' });
              await AppointmentService.sendPUSHToUser(token, title, body, 'appointment', requestId || '');
              console.log('FCM notification sent successfully');
            }
          } else {
            console.log('No FCM token found for mechanic:', mechanicId);
          }
        } catch (nfErr) {
          console.warn('Failed to send approve notification to mechanic (non-fatal):', nfErr);
        }
      } else {
        console.log('No mechanicId found, skipping FCM notification');
      }
    } catch (error) {
      console.error('Error approving appointment:', error);
      throw error;
    }
  },

  uploadImage: async (
    imageUri: string,
    appointmentId: string,
    index: number,
    folder: string,
  ) => {
    try {
      const {
        getStorage,
        ref: storageRef,
        uploadBytes,
        getDownloadURL,
      } = require('@react-native-firebase/storage');
      const storage = getStorage();
      const filename = `${appointmentId}_${index}_${Date.now()}.jpg`;
      const reference = storageRef(storage, `${folder}/${filename}`);
      const response = await fetch(imageUri);
      const blob = await response.blob();
      await uploadBytes(reference, blob);
      const downloadURL = await getDownloadURL(reference);
      return downloadURL;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  },

  updateCancelAppointmentImagePath: async (
    uploadArray: string[],
    appointmentId: string,
  ) => {
    try {
      const db = getDatabase();
      await update(ref(db, `appointment/${appointmentId}/cancelled-state`), {
        images: uploadArray.join(','),
      });
    } catch (error) {
      console.error('Error updating cancel appointment image path:', error);
      throw error;
    }
  },

  fetchSessionDetail: (sessionId: string) => {
    const db = getDatabase();
    return query(ref(db, 'session'), orderByKey(), equalTo(sessionId));
  },

  fetchInvoiceGenerated: (wrkReqId: string) => {
    const db = getDatabase();
    return query(ref(db, 'invoice'), orderByKey(), equalTo(wrkReqId));
  },

  fetchWorkRequest: (workRequestId: string) => {
    const db = getDatabase();
    return query(ref(db, 'work-request'), orderByKey(), equalTo(workRequestId));
  },
};
