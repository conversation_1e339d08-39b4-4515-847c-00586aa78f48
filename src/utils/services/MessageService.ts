import { getDatabase, ref, child, query, orderByChild, equalTo, startAt, endAt, set } from '@react-native-firebase/database';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GC_CUSTOMER_ID } from '../globals';


export class MessageService {
  static async getServiceMessages() {
    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    if (!customerId) {throw new Error('Customer ID not found');}
    const db = getDatabase();
    const messageRef = ref(db, 'message');
    return query(messageRef, orderByChild('customer'), equalTo(customerId));
  }

  static async fetchWorkRequest(workRequestId: string) {
    const db = getDatabase();
    return ref(db, `work-request/${workRequestId}`);
  }

static async getPendingRequest() {
  const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
  if (!customerId) throw new Error('Customer ID not found');
  const db = getDatabase();
  const customerRef = child(child(ref(db, 'customer'), customerId), 'work-requests');
  const queryRef = query(customerRef, orderByChild('status'), equalTo('pending'));
  console.log('Pending query path:', queryRef.toString());  // Log the query
  return queryRef;
}
  static async readServiceMessage(messageId: string) {
    const db = getDatabase();
    const readRef = child(child(ref(db, 'message'), messageId), 'customer-read');
    return set(readRef, true);
  }

  static async readSystemMessage(messageId: string) {
    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    if (!customerId) {throw new Error('Customer ID not found');}
    const db = getDatabase();
    const readRef = child(child(child(ref(db, 'system-message'), messageId), 'read'), customerId);
    return set(readRef, true);
  }

  static async deleteServiceMessage(messageId: string) {
    const db = getDatabase();
    const deleteRef = child(child(ref(db, 'message'), messageId), 'customer-delete');
    return set(deleteRef, true);
  }

  static async deleteSystemMessage(messageId: string) {
    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    if (!customerId) {throw new Error('Customer ID not found');}
    const db = getDatabase();
    const deleteRef = child(child(child(ref(db, 'system-message'), messageId), 'delete'), customerId);
    return set(deleteRef, true);
  }

  static async getSystemMessages() {
    const db = getDatabase();
    return ref(db, 'system-message');
  }

  static async fetchSessionDetail(sessionId: string) {
    const db = getDatabase();
    return ref(db, `session/${sessionId}`);
  }

  static async fetchAppointmentDetails(appointmentId: string) {
    const db = getDatabase();
    return ref(db, `appointment/${appointmentId}`);
  }

}
