import { getDatabase, ref, child, push, set, update } from '@react-native-firebase/database';
import Config from 'react-native-config';

export type ChatLogPayload = {
  'mechanic-id': string;
  'mechanic-name': string;
  'mechanic-image': string;
  'customer-id': string;
  'customer-name': string;
  'customer-image': string;
  'last-message': string;
  'last-sender': string;
  'service': any;
  'date': any;
  'month': any;
  'time': string;
  'updated-time': number;
  'type': string;
  'work-request-id': string;
  'seen': boolean;
};

export const ChatService = {
  roomKey(customerId: string, mechanicId: string, workRequestId: string) {
    return `${customerId}_${mechanicId}_${workRequestId}`;
  },

  chatRef(roomKey: string) {
    const db = getDatabase();
    return child(ref(db, 'chat'), roomKey);
  },

  chatReadRef(roomKey: string) {
    const db = getDatabase();
    return child(ref(db, 'chat-read'), roomKey);
  },

  chatLogRef(roomKey: string) {
    const db = getDatabase();
    return child(ref(db, 'chat-log'), roomKey);
  },

  customerMyChatsRef(customerId: string, roomKey: string) {
    const db = getDatabase();
    return child(child(child(ref(db, 'customer'), customerId), 'my-chats'), roomKey);
  },
   allChatsRef(customerId: string) {
    const db = getDatabase();
    return child(child(ref(db, 'customer'), customerId), 'my-chats');
  },


  async sendMessage(roomKey: string, payload: any, chatLog: ChatLogPayload, customerId: string) {
    // const db = getDatabase();
    const chatRef = this.chatRef(roomKey);
    const msgKey = (await push(chatRef)).key as string;
    await set(child(chatRef, msgKey), payload);
    await set(child(this.chatReadRef(roomKey), msgKey), false);
    await set(this.chatLogRef(roomKey), chatLog);
    await set(this.customerMyChatsRef(customerId, roomKey), true);
    
    return msgKey;
  },

  async deleteAllCustomerMessages(roomKey: string, customerId: string, messageIds: string[]) {
   const updates: Record<string, any> = {};
    messageIds.forEach(id => {
      updates[`${id}/customerDelete`] = true;
    });
    await update(this.chatRef(roomKey), updates);
    const userRef = this.fetchUserInformation(customerId);
    if (userRef) {
      userRef.child('my-chats').child(roomKey).remove();
    }
  },

  async deleteSelectedCustomerMessages(roomKey: string, messageIds: string[]) {
    // const db = getDatabase();
    const updates: Record<string, any> = {};
    messageIds.forEach(id => {
      updates[`${id}/customerDelete`] = true;
    });
    if (Object.keys(updates).length > 0) {
      await update(this.chatRef(roomKey), updates);
    }
  },
    getMechanicDetail(mechanicId: string) {
    const db = getDatabase();
    return ref(db,'mechanic').child(mechanicId);
  },
    fetchUserInformation(customerId: string) {
     const db = getDatabase();
    if (customerId == undefined || customerId == null) return;
  return ref(db,'customer').child(customerId);
  },

 async sendFCMNotification(notfTitle: string, notfBody: string, notfTo: any, type: string, params: string) {
  let fcmToken = Config.FCM_NOTIFICATION_KEY;
  console.log('fcmToken',fcmToken)
    let body = {
      "notification": {
        "title": notfTitle,
        "body": notfBody,
        "sound": "default",
        "click_action": "FCM_PLUGIN_ACTIVITY",
        "icon": "fcm_push_icon"
      },
      "data": {
        "type": type,
        "params": params
      },

      "to": notfTo,
      "priority": "high",
      "restricted_package_name": ""
    } 
    try {
      const response = await fetch('https://fcm.googleapis.com/fcm/send', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: fcmToken as string,
        },
        body: JSON.stringify(body),
      });
      console.log('respp', response);
    } catch (error) {
      console.error('Error sending notification:', error);
      throw error;
    }
  }
};
