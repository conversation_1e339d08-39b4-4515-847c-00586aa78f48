/**
 * Card utility functions
 */

export const matchCardIcon = (cardBrand: string) => {
  const brand = cardBrand?.toLowerCase().trim();
  
  switch (brand) {
    case 'visa':
    case 'visa (debit)':
      return require('../../assets/images/v.png');
    
    case 'mastercard':
    case 'mastercard (2-series)':
    case 'mastercard (debit)':
    case 'mastercard (prepaid)':
      return require('../../assets/images/m.png');
    
    case 'american express':
    case 'amex':
      return require('../../assets/images/a.png');
    
    case 'discover':
      return require('../../assets/images/d.png');
    
    case 'diners club':
      return require('../../assets/images/dc.png');
    
    case 'jcb':
      return require('../../assets/images/j.png');
    
    case 'unionpay':
      return require('../../assets/images/u.png');
    
    default:
      return require('../../assets/images/m.png');
  }
};

export const getCardBrandName = (brand: string): string => {
  const brandLower = brand?.toLowerCase();
  
  if (brandLower?.includes('visa')) return 'Visa';
  if (brandLower?.includes('mastercard')) return 'Mastercard';
  if (brandLower?.includes('amex') || brandLower?.includes('american express')) 
    return 'American Express';
  if (brandLower?.includes('discover')) return 'Discover';
  if (brandLower?.includes('diners')) return 'Diners Club';
  if (brandLower?.includes('jcb')) return 'JCB';
  if (brandLower?.includes('unionpay')) return 'UnionPay';
  
  return 'Credit Card';
};