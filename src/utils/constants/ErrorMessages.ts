export const ERROR_MESSAGE_FOR_SIGNUP = {
  firstname: [
    {
      type: 'required',
      message: 'First name is required',
    },
  ],
  lastname: [
    {
      type: 'required',
      message: 'Last name is required',
    },
  ],
  email: [
    {
      type: 'required',
      message: 'Email is required',
    },
    {
      type: 'email',
      message: 'Please enter a valid email address',
    },
  ],
  mobile: [
    {
      type: 'required',
      message: 'Mobile number is required',
    },
    {
      type: 'minlength',
      message: 'Mobile number must be at least 14 characters',
    },
  ],
  gender: [
    {
      type: 'required',
      message: 'Gender is required',
    },
  ],
  maritalstatus: [
    {
      type: 'required',
      message: 'Marital status is required',
    },
  ],
  dateofbirth: [
    {
      type: 'required',
      message: 'Date of birth is required',
    },
  ],
  locationInput: [
    {
      type: 'required',
      message: 'Location is required',
    },
  ],
  coordinates: [
    {
      type: 'required',
      message: 'Coordinates are required',
    },
  ],
  address1: [
    {
      type: 'required',
      message: 'Address 1 is required',
    },
  ],
  address2: [
    {
      type: 'required',
      message: 'Address 2 is required',
    },
  ],
  city: [
    {
      type: 'required',
      message: 'City is required',
    },
  ],
  state: [
    {
      type: 'required',
      message: 'State is required',
    },
  ],
  zipcode: [
    {
      type: 'required',
      message: 'Zip code is required',
    },
  ],
  country: [
    {
      type: 'required',
      message: 'Country is required',
    },
  ],
};
