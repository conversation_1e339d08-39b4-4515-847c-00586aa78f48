export const AppStrings = {
  LOGIN_STRINGS: {
    //Login main screen strings
    MCX_TERMS_PREFIX: 'BY LOGGING INTO OR SIGNING UP, YOU AGREE TO OUR',
    MCX_PRIVACY_POLICY: 'PRIVACY POLICY',
    MC<PERSON>_TERMS_OF_SERVICE: 'TERMS OF SERVICE',
    MCX_AND_KEYWORD: '   AND   ',
    MCX_SIGN_UP: 'SIGN UP',
    MCX_LOGIN: 'LOGIN',
    MCX_OR_WITH: 'OR WITH',
    //User login screen strings
    MCX_ENTER_EMAIL: 'ENTER YOUR EMAIL ADDRESS',
    MCX_ENTER_PASSWORD: 'ENTER PASSWORD',
    MCX_FORGOT_PASSWORD: 'Forgot password',
  },

  SIGNUP_STRINGS: {
    HEADERS: {
      ACCOUNT_REGISTRATION: 'ACCOUNT REGISTRATION',
      PERSONAL_INFORMATION: 'PERSONAL INFORMATION',
      CREATE_PASSWORD: 'CREATE PASSWORD',
      MY_SERVICE: 'MY SERVICE',
      SECTION_4: 'SECTION 4',
    },
    FIELDS: {
      FIRST_NAME: 'FIRST NAME',
      LAST_NAME: 'LAST NAME',
      EMAIL: 'EMAIL',
      MOBILE: 'MOBILE NUMBER',
      PASSWORD: 'PASSWORD',
      CONFIRM_PASSWORD: 'CONFIRM PASSWORD',
      ADDRESS: 'ADDRESS',
      CITY: 'CITY',
      STATE: 'STATE',
      ZIP_CODE: 'ZIP CODE',
    },
    SERVICES: {
      OIL_CHANGE: {
        id: 'oilChange',
        title: 'OIL CHANGE',
      },
      ROADSIDE: {
        id: 'roadside',
        title: 'ROADSIDE\nASSISTANCE',
      },
      DIAGNOSTICS: {
        id: 'diagnostics',
        title: 'DIAGNOSTICS &\nINSPECTION',
      },
      REPAIR: {
        id: 'repair',
        title: 'REPAIR',
      },
    },
    BUTTONS: {
      CONTINUE: 'CONTINUE',
    },
    PLACEHOLDERS: {
      MOBILE: '(*************',
    },
    KEYS: {
      FIRST_NAME_KEY: 'firstName',
      LAST_NAME_KEY: 'lastName',
      EMAIL_KEY: 'email',
      MOBILE_KEY: 'mobile',
      PASSWORD_KEY: 'password',
      CONFIRM_PASSWORD_KEY: 'confirmPassword',
      ADDRESS_KEY: 'address',
      CITY_KEY: 'city',
      STATE_KEY: 'state',
      ZIP_CODE_KEY: 'zipCode',
    },
    ALERT_STRINGS: {
      ALERT_TOOLKIT_TITLE: 'Tool kit confirmation',
      ALERT_TOOLKIT_HEADING:
        'Filter wrench, Funnel, Wrench for plug, Used oil pan, Jack',
      ALERT_TOOLKIT_CONTENT:
        'Please confirm that you have all the mentioned tools available with you. Please uncheck otherwise.',
      ALERT_CONFIRM_BUTTON: 'CONFIRM',
    },
    PERSONAL_INFO: 'PERSONAL INFORMATION',
    CREATE_PASSWORD: 'CREATE PASSWORD',
    MY_SERVICE: 'MY SERVICE',
    SECTION_4: 'SECTION 4',
  },

  DASHBOARD_STRINGS: {
    NO_REQUESTS_TEXT: 'No Requests',
  },

  PROFILE_STRINGS: {
    HEADERS: {
      MY_PROFILE: 'MY PROFILE',
      MY_DETAILS: 'MY DETAILS',
      MY_SERVICES: 'MY SERVICES',
      SERVICES: 'SERVICES',
      MECHANICAL_SESSIONS: 'MECHANICAL SESSIONS',
      PAYMENT_DETAILS: 'PAYMENT DETAILS',
      AC_DELETION: 'DEACTIVATE ',
      IDENTIFICATIONS: 'IDENTIFICATIONS',
      ADD_IDENTIFICATION: 'ADD IDENTIFICATION',
      CERTIFICATIONS: 'CERTIFICATIONS',
      ADD_CERTIFICATION: 'ADD CERTIFICATION',
      COMPLETED_SESSIONS: 'COMPLETED SESSIONS',
      DECLINED_SESSIONS: 'DECLINED SESSIONS',
      CANCELLED_SESSIONS: 'CANCELLED SESSIONS',
      ACCOUNT_DETAILS: 'ACCOUNT DETAILS',
    },
    PROFILE_INFO: {
      NAME: 'John Mathew',
      LOCATION: 'Angamaly',
      SUB_LOCATION: 'Kerala, India',
    },
    DETAIL_ROWS: {
      PROFILE_DETAILS: {
        LABEL: 'Profile Details',
        ACTION: 'Edit Details',
      },
      IDENTIFICATIONS: {
        LABEL: 'Identifications',
        ACTION: 'Details',
      },
      CERTIFICATIONS: {
        LABEL: 'Certifications',
        ACTION: 'Details',
      },
      ROADSIDE_ASSISTANCE: {
        LABEL: 'Roadside Assistance',
        ACTION: 'Edit Details',
      },
      COMPLETED_SESSION: {
        LABEL: 'Completed Session',
        ACTION: 'Details',
      },
      DECLINED_SESSION: {
        LABEL: 'Declined Session',
        ACTION: 'Details',
      },
      PAYMENT_SESSION: {
        LABEL: 'New Payment Type',
        ACTION: 'Add',
      },
      AC_DELETION_SESSION: {
        LABEL: 'Delete',
        ACTION: 'DELETE',
      },
      AC_DEACTIVATION_SESSION: {
        LABEL: 'Deactivate',
        ACTION: 'DEACTIVATE',
      },
    },
    LABELS: {
      FIRST_NAME: 'FIRST NAME',
      LAST_NAME: 'LAST NAME',
      EMAIL: 'EMAIL',
      MOBILE: 'MOBILE',
      EXPERIENCE: 'EXPERIENCE',
      GENDER: 'GENDER',
      DOB: 'D.O.B',
      LOCATION: 'LOCATION',
      ADDRESS1: 'ADDRESS 1',
      ADDRESS2: 'ADDRESS 2',
      CITY: 'CITY',
      STATE: 'STATE',
      ZIP: 'ZIP CODE',
      COUNTRY: 'COUNTRY',
    },
    PLACEHOLDERS: {
      FIRST_NAME: 'Enter first name',
      LAST_NAME: 'Enter last name',
      EMAIL: 'Email',
      MOBILE: 'Enter mobile number',
      EXPERIENCE: 'Select experience',
      GENDER: 'Select gender',
      DOB: 'Select date of birth',
      LOCATION: 'Select your location',
      ADDRESS1: 'Enter address line 1',
      ADDRESS2: 'Enter address line 2',
      CITY: 'Enter city',
      STATE: 'Enter state',
      ZIP: 'Enter zip code',
      COUNTRY: 'Enter country',
    },
    BUTTONS: {
      CONTINUE: 'CONTINUE',
      CHANGE_PHOTO: 'Change Photo',
      GPS: 'GPS',
      UPLOADING: 'Uploading...',
      GETTING_LOCATION: 'Getting location...',
      ADD_IDENTIFICATION: 'ADD IDENTIFICATION',
      ADD_CERTIFICATION: 'ADD CERTIFICATION',
      SAVE: 'SAVE',
    },
  },

  REQUEST_STRINGS: {
    TOGGLE_BUTTONS: {
      SINGLE: 'SINGLE',
      POOL: 'MYCANX POOL',
    },
    STATS: {
      NEAR_ME: 'NEAR ME',
      RIGHT_NOW: 'RIGHT NOW',
      APPOINTMENT: 'APPOINTMENT',
    },
    CANCELLATION_RATE: 'Cancellation Rate',
    AVAILABILITY: {
      BY_AVAILABILITY: 'By Availability',
      BY_APPOINTMENT: 'By Appointment',
    },
    APPOINTMENT: 'Appointment',
  },

  CALENDAR_STRINGS: {
    NO_APPOINTMENTS: 'No Appointments',
  },
  MESSAGES_STRINGS: {
    HEADERS: {
      MESSAGES: 'MESSAGES',
    },
    NO_MESSAGES: 'no messages',
    TABS: {
      ALL: 'ALL',
      APPOINTMENT: 'APPOINTMENT',
      SESSION: 'SESSION',
      SYSTEM: 'SYSTEM',
    },
  },
  REFER_FRIEND_STRINGS: {
    HEADERS: {
      REFER_A_FRIEND: 'REFER A FRIEND',
    },
    EARNED_PREFIX: 'Earned : ',
    MAIN_MESSAGE: 'Send your friends free point',
    DESCRIPTION:
      'Share the myCANx love and give friends free point to try myCANx services.',
    SHARE_MESSAGE: 'Join me on myCANx! Use my referral link: ',
    BUTTONS: {
      SHARE_INVITE_LINK: 'SHARE INVITE LINK',
      REDEEM_POINTS: 'REDEEM POINTS',
    },
    DEFAULT_AMOUNT: '$0',
    DEFAULT_LINK: 'https://u6zc.app.link/qQYlv64RXxb',
  },

  SETTINGS_STRINGS: {
    HEADERS: {
      SETTINGS: 'SETTINGS',
    },
    SECTIONS: {
      GPS_SETTINGS: 'GPS SETTINGS',
      NOTIFICATIONS: 'NOTIFICATIONS',
    },
    NOTIFICATIONS: {
      IN_APP: 'In Application',
      TEXT: 'Text',
      EMAIL: 'Email',
    },
  },
  HELP_STRINGS: {
    HEADERS: {
      HELP: 'HELP',
      HELP_DETAILS: 'HELP DETAILS',
      ABOUT: 'ABOUT',
      LEGAL: 'LEGAL',
      TERMS_AND_CONDITIONS: 'TERMS & CONDITIONS',
      PRIVACY_POLICY: 'PRIVACY POLICY',
    },
    SECTIONS: {
      LEGAL_DOCUMENTS: 'LEGAL DOCUMENTS',
      HELP_TOPICS: 'FAQ',
    },
    ITEMS: {
      HELP: 'Help',
      LEGAL: 'Legal',
      ABOUT: 'About',
    },
  },
  SERVICES_STRINGS: {
    HEADERS: {
      MY_SERVICES: 'MY SERVICES',
      SERVICES: 'SERVICES',
      MECHANICAL_SERVICES: 'MECHANICAL SERVICES',
      ADD_SERVICE: 'ADD SERVICE',
    },
    BUTTONS: {
      ADD_SERVICE: 'ADD SERVICE',
      SAVE_SERVICE: 'SAVE SERVICE',
      DELETE_SERVICE: 'DELETE SERVICE',
      BACK: 'Back',
      ADD_SUBSERVICE: 'Add Subservice',
    },
    LABELS: {
      SERVICE_TYPE: 'SERVICE TYPE',
      SUBSERVICE: 'SUBSERVICE',
      AVAILABILITY: 'Availability',
      EXPERIENCE: 'EXPERIENCE',
      CERTIFICATIONS: 'CERTIFICATIONS',
      LOCATION: 'LOCATION',
    },
    PLACEHOLDERS: {
      SELECT_SERVICE_TYPE: 'SERVICE TYPE',
      SELECT_SUBSERVICE: 'SUBSERVICE',
    },
    MESSAGES: {
      SERVICE_SAVED: 'Service saved',
      SERVICE_DELETED: 'Service deleted',
      ADD_NEW_SUBSERVICE: 'Add new subservice',
    },
    SERVICE_TYPES: [
      {label: 'Oil Change', value: 'Oil Change'},
      {label: 'Brake Service', value: 'Brake Service'},
      {label: 'Tire Rotation', value: 'Tire Rotation'},
      {label: 'Engine Diagnostics', value: 'Engine Diagnostics'},
    ],
    SUBSERVICES: {
      'Oil Change': [
        {label: 'Conventional', value: 'Conventional'},
        {label: 'Synthetic', value: 'Synthetic'},
        {label: 'Synthetic Blend', value: 'Synthetic Blend'},
      ],
      'Brake Service': [
        {label: 'Disc Brakes', value: 'Disc Brakes'},
        {label: 'Drum Brakes', value: 'Drum Brakes'},
        {label: 'Brake Fluid Flush', value: 'Brake Fluid Flush'},
      ],
      'Tire Rotation': [
        {label: 'Standard Rotation', value: 'Standard Rotation'},
        {label: 'Cross Pattern', value: 'Cross Pattern'},
      ],
      'Engine Diagnostics': [
        {label: 'Computer Scan', value: 'Computer Scan'},
        {label: 'Visual Inspection', value: 'Visual Inspection'},
        {label: 'Performance Testing', value: 'Performance Testing'},
      ],
    },
    ALERTS: {
      DELETE_SERVICE_TITLE: 'DELETE SERVICE',
      DELETE_SERVICE_CONFIRMATION:
        'Are you sure you want to delete this service',
      YES: 'YES',
      NO: 'NO',
    },
  },
  SIDEMENU_STRINGS: {
    AVAILABILITY: 'Availability',
    LOG_OUT: 'LOGOUT',
    VERSION: 'v1.7.4',
    LOGOUT_ALERT: {
      TITLE: 'LOGOUT',
      HEADING: 'Are you sure you want to logout?',
      CONTENT:
        'You will be redirected to the login screen and will need to sign in again.',
      PRIMARY_BUTTON: 'LOGOUT',
      SECONDARY_BUTTON: 'CANCEL',
    },
  },
  RESET_PASSWORD_STRINGS: {
    RESET_FAILED: 'Reset Password Failed',
    ALERADY_REGISTERED_EMAIL:
      'Your email is already registered in another myCANx application. Please sign up for another account, or simply use a different email for this app.',
    CHECK_MAIL: 'Check Mail',
    CHECK_INBOX: 'Check your inbox for a password reset link',
    WENT_WRONG: 'Something went wrong. Please try again',
    ERROR: 'Error',
    ENTER_VALID_MAIL: 'Please enter your email address',
    OK: 'OK',
  },
} as const;
