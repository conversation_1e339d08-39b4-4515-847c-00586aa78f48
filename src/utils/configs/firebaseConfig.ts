import { getAnalytics, setAnalyticsCollectionEnabled } from '@react-native-firebase/analytics';
import { getApp } from '@react-native-firebase/app';
import '@react-native-firebase/auth';
import '@react-native-firebase/database';

const initializeFirebase = async () => {
  try {
    const app = getApp();
    const analyticsInstance = getAnalytics(app);
    await setAnalyticsCollectionEnabled(analyticsInstance, true);
    return analyticsInstance;
  } catch (error) {
    console.error('Error initializing Firebase:', error);
    throw error;
  }
};

export default initializeFirebase;
