// utils/helpers/colorUtils.ts

/**
 * Converts hex to rgba string
 * @param hex - Hex color string (#RRGGBB)
 * @param opacity - Opacity as decimal (0.0 - 1.0)
 */
export const hexToRGBA = (hex: string, opacity: number): string => {
    const sanitizedHex = hex.replace('#', '');

    if (sanitizedHex.length !== 6) {
        console.warn('Invalid hex color passed to hexToRGBA:', hex);
        return hex;
    }

    const bigint = parseInt(sanitizedHex, 16);
    const r = (bigint >> 16) & 255;
    const g = (bigint >> 8) & 255;
    const b = bigint & 255;

    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

/**
 * Darkens a hex color by a given amount
 * @param hex - Hex color string (#RRGGBB)
 * @param amount - Amount to darken (0.0 to 1.0) — 0 = no change, 1 = black
 * @returns Darkened hex string
 */
export const darkenHexColor = (hex: string, amount: number): string => {
    const sanitizedHex = hex.replace('#', '');

    if (sanitizedHex.length !== 6) {
        console.warn('Invalid hex color passed to darkenHexColor:', hex);
        return hex;
    }

    const num = parseInt(sanitizedHex, 16);

    let r = (num >> 16) & 255;
    let g = (num >> 8) & 255;
    let b = num & 255;

    r = Math.max(0, Math.floor(r * (1 - amount)));
    g = Math.max(0, Math.floor(g * (1 - amount)));
    b = Math.max(0, Math.floor(b * (1 - amount)));

    const darkened = (r << 16) + (g << 8) + b;
    return `#${darkened.toString(16).padStart(6, '0')}`;
};