export const GC_NEAR_MILES = 10;

export const calculateDistance = (lat1: number, long1: number, lat2: number, long2: number): number | string => {
  if (lat1 === 0 || lat2 === 0) {
    return '--';
  }
  const p = 0.017453292519943295;
  const c = Math.cos;
  const a = 0.5 - c((lat1 - lat2) * p) / 2 + c(lat2 * p) * c(lat1 * p) * (1 - c((long1 - long2) * p)) / 2;
  const dis = 12742 * Math.asin(Math.sqrt(a));
  const rounded = Math.round(dis * 10) / 10;
  return rounded;
};
