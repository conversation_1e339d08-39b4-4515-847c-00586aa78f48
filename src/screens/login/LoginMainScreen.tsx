import React, {useEffect, useState} from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ImageStyle,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {AppStrings, RouteNames} from '../../utils/constants/AppStrings';
import {useNavigation} from '@react-navigation/native';
import {Colors, Fonts} from '../../utils/constants/Theme';
import HorizontalDivider from '../../components/common/HorizontalDivider';
import GoogleAuthService from '../../auth/google/googleAuthService';
import {AnalyticService} from '../../utils/services/AnalyticService';
import {
  FacebookAuthProvider,
  getAuth,
  signInWithCredential,
  AppleAuthProvider,
} from '@react-native-firebase/auth';
import {<PERSON>ginManager, AccessToken} from 'react-native-fbsdk-next';

import {
  GC_CUSTOMER_ID,
  GC_SIGNUP_PROVIDER_TYPE_ID_PREF,
  GC_SIGNUP_PROVIDER_TYPE_PREF,
  GC_APPLE_FIRST_NAME,
  GC_APPLE_EMAIL,
} from '../../utils/globals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import appleAuth from '@invertase/react-native-apple-authentication';
import {useAuth} from '../../utils/configs/AuthContext';

const LoginMainScreen = () => {
  const navigation = useNavigation();
  const {signIn} = GoogleAuthService();
  const {AddAnalyticsScreenView} = AnalyticService;
  const {fetchUserInfo, isRegisteredAsMechanicUser} = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    AddAnalyticsScreenView('WelcomeLogin Page', '/#/welcome-login');
  }, [AddAnalyticsScreenView]);

  const onSignupButtonPress = async () => {
    await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'normal');
    await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF, '');
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
    navigation.navigate(RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never);
  };

  const onSignInButtonPress = async () => {
    await AsyncStorage.setItem(GC_CUSTOMER_ID, '');
    navigation.navigate(RouteNames.MCX_NAV_AppLoginPageScreen as never);
  };
  const onGoogleButtonPress = async () => {
    try {
      const loggedUser = await signIn();
      
      // If user cancelled the sign-in, return early without showing loader
      if (!loggedUser) {
        console.log('Google Sign-In cancelled by user');
         Alert.alert(
          'Sign-In Cancelled',
          'You cancelled the Google sign-in process. Please try again if you want to access the app.',
          [{ text: 'OK', style: 'default' }]
        );
        return;
      }
      
      // Only show loading after successful sign in
      setIsLoading(true);
      
      // Store user data in AsyncStorage
      await Promise.all([
        AsyncStorage.setItem(GC_CUSTOMER_ID, loggedUser.uid),
        AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'google'),
        AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_ID_PREF, loggedUser.providerId),
        
      ]);

      const isMechanic = await isRegisteredAsMechanicUser(loggedUser.uid);
      const userInfo = await fetchUserInfo(loggedUser.uid);
       if (isMechanic || userInfo) {
        Alert.alert(
          'Unable to Login',
          'Your email is already registered in another myCANx application. Please sign up for another account, or simply use a different email for this app.',
        );
        setIsLoading(false);
        return;
      }
      if (userInfo?.['registration-status'] === 'COMPLETED') {
        navigation.navigate(RouteNames.MCX_NAV_DashBoard as never);
      } else {
        navigation.navigate(RouteNames.MCX_NAV_ACCOUNT_REGISTRATION as never);
      }
    } catch (error: any) {
      console.error('Google Sign-In Error:', error);
      if (error.message !== 'Sign-in was cancelled') {
        Alert.alert('Sign-In Error', error.message || 'Google Sign-In failed');
      }
    } finally {
      setTimeout(() => {
        setIsLoading(false);
      }, 300);
    }
  };

  const onFacebookButtonPress = async () => {
    try {
      const result = await LoginManager.logInWithPermissions([
        'public_profile',
        'email',
      ]);

      if (result.isCancelled) {
        console.log('Facebook Sign-In cancelled by user');
        Alert.alert(
          'Sign-In Cancelled',
          'You cancelled the Facebook sign-in process. Please try again if you want to access the app.',
          [{ text: 'OK', style: 'default' }]
        );
        return;
      }
      const data = await AccessToken.getCurrentAccessToken();
      if (!data) {
        throw 'Something went wrong obtaining access token';
      }
      const facebookCredential = FacebookAuthProvider.credential(
        data.accessToken,
      );
      const userCredential = await signInWithCredential(
        getAuth(),
        facebookCredential,
      );
      await AsyncStorage.setItem(GC_CUSTOMER_ID, userCredential.user.uid);
      await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'facebook');
      await AsyncStorage.setItem(
        GC_SIGNUP_PROVIDER_TYPE_ID_PREF,
        userCredential.user.uid,
      );

      navigation.reset({
        index: 0,
        // @ts-ignore
        routes: [{name: RouteNames.MCX_NAV_ACCOUNT_REGISTRATION}],
      });
    } catch (error: any) {
      console.error('Facebook Sign-In Error:', error);
      if (error.message !== 'User cancelled the login process') {
        Alert.alert('Sign-In Error', error.message || 'Facebook Sign-In failed');
      }
    }
  };

  const onAppleButtonPress = async () => {
    try {
      setIsLoading(true);
      const rawNonce = Math.random().toString(36).substring(2, 15);
      const state = Math.random().toString(36).substring(2, 15);

      const appleAuthRequestResponse = await appleAuth.performRequest({
        requestedOperation: appleAuth.Operation.LOGIN,
        requestedScopes: [appleAuth.Scope.EMAIL, appleAuth.Scope.FULL_NAME],
        nonce: rawNonce,
        state,
      });
      const {identityToken, nonce: returnedNonce, email, fullName} = appleAuthRequestResponse;
      if (!identityToken) {
        throw new Error('No identity token returned from Apple Sign-In');
      }
      const appleCredential = AppleAuthProvider.credential(identityToken, returnedNonce || rawNonce);
      console.log(appleCredential, 'appleCredential');
      const userCredential = await signInWithCredential(
        getAuth(),
        appleCredential,
      );
      await AsyncStorage.setItem(GC_CUSTOMER_ID, userCredential.user.uid);
      await AsyncStorage.setItem(GC_SIGNUP_PROVIDER_TYPE_PREF, 'apple');
      await AsyncStorage.setItem(
        GC_SIGNUP_PROVIDER_TYPE_ID_PREF,
        userCredential.user.providerId,
      );

      if (fullName) {
        const givenName = fullName.givenName || '';
        const familyName = fullName.familyName || '';
        const displayName = `${givenName} ${familyName}`.trim();
        await userCredential.user.updateProfile({ displayName });
        const nameString = `${givenName}|${familyName}`;
        await AsyncStorage.setItem(GC_APPLE_FIRST_NAME, nameString);
      }
      if (email) {
        await AsyncStorage.setItem(GC_APPLE_EMAIL, email);
      }

      const isMechanic = await isRegisteredAsMechanicUser(userCredential.user.uid);
      const userInfo = await fetchUserInfo(userCredential.user.uid);
      console.log(userInfo, 'userInfo');
      if (isMechanic || userInfo) {
        Alert.alert(
          'Unable to Login',
          'Your email is already registered in another myCANx application. Please sign up for another account, or simply use a different email for this app.',
        );
        setIsLoading(false);
        return;
      }
      if (userInfo?.['registration-status'] === 'COMPLETED') {
        navigation.reset({
          index: 0,
          // @ts-ignore
          routes: [{name: RouteNames.MCX_NAV_DashBoard}],
        });
      } else {
        navigation.reset({
          index: 0,
          // @ts-ignore
          routes: [{name: RouteNames.MCX_NAV_ACCOUNT_REGISTRATION}],
        });
      }
    } catch (error: any) {
      console.error('Apple Sign In Error', error);
      
      // Check if user cancelled the Apple Sign-In
      if (error.code === appleAuth.Error.CANCELED || error.message?.includes('cancelled') || error.message?.includes('canceled')) {
        console.log('Apple Sign-In cancelled by user');
        Alert.alert(
          'Sign-In Cancelled',
          'You cancelled the Apple sign-in process. Please try again if you want to access the app.',
          [{ text: 'OK', style: 'default' }]
        );
        setIsLoading(false);
        return;
      }
      
      Alert.alert('Error', error.message || 'Apple Sign-In failed');
    } finally {
      setIsLoading(false);
    }
  };

  const isAppleSignInSupported = () => {
    try {
      const supported = (appleAuth as any)?.isSupported;
      if (typeof supported === 'boolean') {
        return supported;
      }
      return Platform.OS === 'ios';
    } catch (e) {
      return Platform.OS === 'ios';
    }
  };
  return (
    <View style={modernStyles.container}>
      {isLoading && (
        <View style={modernStyles.loadingOverlay}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
        </View>
      )}
      <Image
        source={require('../../assets/mycanx_logo.png')}
        style={modernStyles.logo}
        resizeMode="contain"
      />

      <TouchableOpacity
        style={modernStyles.signUpBtn}
        onPress={onSignupButtonPress}>
        <Text style={modernStyles.signUpText}>{AppStrings.MCX_SIGN_UP}</Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={modernStyles.loginBtn}
        onPress={onSignInButtonPress}>
        <Text style={modernStyles.loginText}>{AppStrings.MCX_LOGIN}</Text>
      </TouchableOpacity>

      <Text style={modernStyles.orText}>{AppStrings.MCX_OR_WITH}</Text>

      <View style={modernStyles.socialRow}>
        <TouchableOpacity
          style={[modernStyles.socialButton, modernStyles.facebookButton]}
          onPress={onFacebookButtonPress}>
          <Icon name="facebook" size={24} color={Colors.BUTTON_TEXT_COLOR} />
        </TouchableOpacity>
        <TouchableOpacity
          style={[modernStyles.socialButton, modernStyles.googleButton]}
          onPress={onGoogleButtonPress}>
          <Image
            source={require('../../assets/social_network_icons/google_icon.png')}
            style={modernStyles.socialIcon}
            resizeMode="contain"
          />
        </TouchableOpacity>
        {Platform.OS === 'ios' && isAppleSignInSupported() && (
          <TouchableOpacity
            style={[modernStyles.socialButton, modernStyles.appleButton]}
            onPress={() => onAppleButtonPress()}>
            <Icon name="apple" size={24} color={Colors.BUTTON_TEXT_COLOR} />
          </TouchableOpacity>
        )}
      </View>
      {/* Divider line */}
      <HorizontalDivider isFullWidth={true} />
      {/* Terms Section */}
      <View style={modernStyles.termsSection}>
        <Text style={modernStyles.termsText}>
          {AppStrings.MCX_TERMS_PREFIX}
          {'\n'}
          <Text
            style={modernStyles.linkText}
            onPress={() =>
              navigation.navigate(RouteNames.MCX_NAV_PRIVACY_POLICY as never)
            }>
            {AppStrings.MCX_PRIVACY_POLICY}
          </Text>
          {AppStrings.MCX_AND_KEYWORD}
          <Text
            style={modernStyles.linkText}
            onPress={() => {
              console.log('Navigating to TermsConditionsScreen');
              navigation.navigate(RouteNames.MCX_NAV_TERMS_CONDITIONS as never);
            }}>
            {AppStrings.MCX_TERMS_OF_SERVICE}
          </Text>
        </Text>
      </View>
    </View>
  );
};

export default LoginMainScreen;

type Styles = {
  container: ViewStyle;
  loadingOverlay: ViewStyle;
  logo: ImageStyle;
  signUpBtn: ViewStyle;
  loginBtn: ViewStyle;
  socialRow: ViewStyle;
  socialButton: ViewStyle;
  facebookButton: ViewStyle;
  googleButton: ViewStyle;
  appleButton: ViewStyle;
  socialIcon: ImageStyle;
  signUpText: TextStyle;
  loginText: TextStyle;
  orText: TextStyle;
  termsSection: ViewStyle;
  termsText: TextStyle;
  linkText: TextStyle;
};

// Modern themed styles - easy to swap back to original
const modernStyles = StyleSheet.create<Styles>({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 24,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  logo: {
    height: 200,
    width: 200,
    marginBottom: 60,
  },
  signUpBtn: {
    backgroundColor: '#A10000',
    paddingVertical: 16,
    paddingHorizontal: 80,
    borderRadius: 12,
    marginBottom: 16,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 0,
    shadowColor: '#A10000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  loginBtn: {
    backgroundColor: '#FFFFFF',
    borderColor: '#A10000',
    borderWidth: 2,
    paddingVertical: 14,
    paddingHorizontal: 80,
    borderRadius: 12,
    marginBottom: 40,
    alignItems: 'center',
    alignSelf: 'stretch',
    marginHorizontal: 0,
  },
  socialRow: {
    flexDirection: 'row',
    marginBottom: 32,
    gap: 20,
  },
  socialButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 0,
    marginHorizontal: 0,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  facebookButton: {
    backgroundColor: '#1877F2',
    borderColor: 'transparent',
  },
  googleButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  appleButton: {
    backgroundColor: '#000000',
    borderColor: 'transparent',
  },
  socialIcon: {
    width: 26,
    height: 26,
  },
  signUpText: {
    color: '#FFFFFF',
    fontSize: 17,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_BOLD,
    letterSpacing: 0.5,
  },
  loginText: {
    color: '#A10000',
    fontSize: 17,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_BOLD,
    letterSpacing: 0.5,
  },
  orText: {
    color: '#6B7280',
    fontSize: 14,
    marginBottom: 24,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
    letterSpacing: 0.3,
  },
  termsSection: {
    paddingBottom: 40,
    paddingTop: 8,
  },
  termsText: {
    fontSize: 13,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  linkText: {
    color: '#00BCD4',
    fontWeight: '600',
    textDecorationLine: 'underline',
  },
});
