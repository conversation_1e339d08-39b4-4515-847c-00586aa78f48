import React, {useCallback, useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  Image,
  FlatList,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  getDatabase,
  onValue,
  ref,
  child,
  DataSnapshot,
  set,
} from '@react-native-firebase/database';
import {GC_CUSTOMER_ID} from '../../../utils/globals';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import {AppCommonIcons} from '../../../utils/constants/AppStrings';
import {Colors, Fonts, Sizes} from '../../../utils/constants/Theme';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import {useNavigation, useFocusEffect} from '@react-navigation/native';
import {RouteNames} from '../../../utils/constants/AppStrings';
import AppBackground from '../../../components/ui/AppBackground';
import {PageHeader} from '../../../components/ui/PageHeader';
import {ChatService} from '../../../utils/services/ChatService';

type ChatItem = Record<string, any> & {
  ['chat-id']: string;
  ['updated-time']?: number;
};

const ChatListScreen: React.FC = () => {
  const navigation = useNavigation<any>();
  const [customerId, setCustomerId] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [chats, setChats] = useState<ChatItem[]>([]);
  const chatListeners = useRef<Record<string, (() => void)>>({});
  const myChatsUnsubRef = useRef<(() => void) | null>(null);

  // Initialize on mount
  useEffect(() => {
    (async () => {
      const cid = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      setCustomerId(cid);
      if (!cid) {
        setLoading(false);
        return;
      }
      attachMyChatsListener(cid);
    })();

    return () => {
      cleanupAllListeners();
    };
  }, []);

  // Refresh chat list when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      console.log('ChatListScreen focused - refreshing data');
      if (customerId) {
        // Force refresh by re-attaching listeners
        cleanupAllListeners();
        attachMyChatsListener(customerId);
      }
      
      return () => {
        // Optional: cleanup when screen loses focus
        // cleanupAllListeners();
      };
    }, [customerId])
  );

  const cleanupAllListeners = () => {
    try {
      console.log('Cleaning up all listeners...');
      
      // Cleanup my-chats listener using unsubscriber
      if (myChatsUnsubRef.current) {
        myChatsUnsubRef.current();
        myChatsUnsubRef.current = null;
      }
      
      // Cleanup all chat-log listeners using unsubscribers
      Object.keys(chatListeners.current).forEach(chatId => {
        if (chatListeners.current[chatId]) {
          chatListeners.current[chatId](); // Call unsubscribe function
          delete chatListeners.current[chatId];
        }
      });
      
      console.log('All listeners cleaned up successfully');
    } catch (error) {
      console.error('Error cleaning up listeners:', error);
    }
  };

  const attachMyChatsListener = useCallback((cid: string) => {
    const db = getDatabase();
    const myChatsRef = child(child(ref(db, 'customer'), cid), 'my-chats');
    
    // Clear existing chats before attaching new listener
    setChats([]);
    
    // onValue returns an unsubscribe function
    const unsubscribe = onValue(
      myChatsRef,
      snapshot => {
        console.log('My chats updated, count:', snapshot.numChildren());
        
        // Clear existing chat detail listeners
        Object.keys(chatListeners.current).forEach(chatId => {
          if (chatListeners.current[chatId]) {
            try {
              chatListeners.current[chatId]();
            } catch (error) {
              console.warn(`Error cleaning up listener for chat ${chatId}:`, error);
            }
            delete chatListeners.current[chatId];
          }
        });
        
        // Reset chats array when my-chats updates
        setChats([]);
        
        // Attach listeners for each chat
        snapshot.forEach(childSnap => {
          const chatId = childSnap.key as string;
          attachChatDetailListener(chatId);
          return undefined;
        });
        
        setLoading(false);
      },
      error => {
        console.error('Error listening to my-chats:', error);
        setLoading(false);
      }
    );
    
    // Store unsubscribe function
    myChatsUnsubRef.current = unsubscribe;
  }, []);

  const attachChatDetailListener = useCallback((chatId: string) => {
    // Prevent duplicate listeners
    if (chatListeners.current[chatId]) {
      console.log('Listener already exists for chatId:', chatId);
      return;
    }

    const chatRef = ChatService.chatLogRef(chatId);
    
    // onValue returns an unsubscribe function
    const unsubscribe = onValue(
      chatRef,
      (snapshot: DataSnapshot) => {
        const val = snapshot.val();
        
        if (!val) {
          // Chat log doesn't exist, remove from list
          console.log('Chat log not found for chatId:', chatId);
          setChats(prev => prev.filter(chat => chat['chat-id'] !== chatId));
          return;
        }

        const chatObj: ChatItem = {...val, ['chat-id']: chatId};
        
        setChats(prev => {
          // Remove existing entry if present (by composite key)
          const filtered = prev.filter(
            e => !(
              e['customer-id'] === chatObj['customer-id'] &&
              e['mechanic-id'] === chatObj['mechanic-id'] &&
              e['work-request-id'] === chatObj['work-request-id']
            )
          );
          
          // Add new/updated entry and sort
          const updated = [...filtered, chatObj];
          updated.sort(compareChatArray);
          
          return updated;
        });
      },
      error => {
        console.error(`Error listening to chat-log/${chatId}:`, error);
      }
    );
    
    // Store unsubscribe function
    chatListeners.current[chatId] = unsubscribe;
  }, []);

  const compareChatArray = (a: ChatItem, b: ChatItem) => {
    const av = a['updated-time'] || 0;
    const bv = b['updated-time'] || 0;
    if (av > bv) return -1;
    if (av < bv) return 1;
    return 0;
  };

  const onPressChat = (index: number) => {
    const item = chats[index];
    if (!item) return;
    
    const senderId = item['last-sender'];
    const seen = item['seen'] === true;
    
    // Mark as seen if last message was from mechanic
    try {
      if (customerId && senderId !== customerId && !seen) {
        const db = getDatabase();
        const chatLogRef = child(ref(db, 'chat-log'), item['chat-id']);
        set(child(chatLogRef, 'seen'), true);
      }
    } catch (error) {
      console.error('Error marking chat as seen:', error);
    }

    const params = {
      mechanicId: item['mechanic-id'],
      customerId: item['customer-id'],
      mechanicImage: item['mechanic-image'],
      mechanicName: item['mechanic-name'],
      workRequestId: item['work-request-id'],
      workRequestDetails: {
        day: item['date'],
        month: item['month'],
        service: item['service'],
        time: item['time'],
      },
    };
    
    navigation.navigate(RouteNames.MCX_NAV_CHAT_ROOM as never, params as never);
  };

  const renderItem = ({item, index}: {item: ChatItem; index: number}) => {
  const lastSender = item['last-sender'];
  const isMine = customerId && lastSender === customerId;
  const mechanicImage = item['mechanic-image'];
  const isNew = !!(
    customerId &&
    lastSender !== customerId &&
    item['seen'] !== true
  );

  // Format service names (handle array or string) - show only first service
  const services = item['service'];
  let serviceText = '';
  if (Array.isArray(services) && services.length > 0) {
    serviceText = typeof services[0] === 'string' ? services[0] : services[0]?.service || '';
  } else if (typeof services === 'string') {
    serviceText = services;
  }

  return (
    <TouchableOpacity
      style={styles.row}
      onPress={() => onPressChat(index)}
      activeOpacity={0.8}>
      <Image
        source={
          mechanicImage
            ? {uri: mechanicImage}
            : AppCommonIcons.MCX_USER_PROFILE_PIC
        }
        style={styles.avatar}
      />
      <View style={styles.content}>
        {serviceText ? (
          <Text style={styles.serviceText} numberOfLines={1}>
           {serviceText}
          </Text>
        ) : null}
        <Text style={styles.mechanic} numberOfLines={1}>
          {item['mechanic-name'] || ''}
        </Text>
        <Text style={styles.lastMessageRed} numberOfLines={1}>
          {item['last-message'] || ''}
        </Text>
      </View>
      <View style={{alignItems: 'flex-end'}}>
        {isMine && (
          <Image
            source={AppCommonIcons.MCX_ARROW_FORWARD}
            style={styles.rightEnvelope}
          />
        )}
        {isNew && <Text style={styles.newBadge}>new</Text>}
      </View>
    </TouchableOpacity>
  );
};

  return (
    <View style={styles.mainContainer}>
      <AppBackground />
      <PageHeader title="CHATS" onBack={() => navigation.goBack()} />
      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={false}>
        <View style={styles.container}>
          {chats.length === 0 && !loading ? (
            <View style={styles.empty}>
              <Text style={styles.emptyText}>No chats found</Text>
            </View>
          ) : (
            <FlatList
              data={chats}
              keyExtractor={item => item['chat-id']}
              renderItem={renderItem}
              contentContainerStyle={styles.listContent}
              extraData={chats} // Force re-render when chats change
            />
          )}
        </View>
        <LoaderOverlay visible={loading} />
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {flex: 1},
  listContent: {paddingHorizontal: 12, paddingTop: 12, paddingBottom: 24},
  row: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    alignItems: 'center',
    borderWidth: 0.5,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 3,
    elevation: 2,
  },
  avatar: {width: 48, height: 48, borderRadius: 24, marginRight: 12},
  content: {flex: 1},
  mechanic: {
    fontSize: Sizes.MEDIUM,
      color:  '#E53E3E',
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '600',
  },
  lastMessageRed: {
    fontSize: Sizes.MEDIUM,
    color: Colors.COMMON_BlACK_SHADE,
    marginTop: 2,
    fontFamily: Fonts.ROBO_REGULAR,
  },
  rightEnvelope: {
    width: 22,
    height: 22,
    tintColor: Colors.PRIMARY,
    marginLeft: 8,
  },
  serviceText: {
  fontSize: Sizes.MEDIUM,
  color: '#000',
  fontFamily: Fonts.ROBO_REGULAR,
  marginTop: 2,
  fontWeight: '700',
},
  newBadge: {
    marginTop: 4,
    backgroundColor: '#E53E3E',
    color: '#fff',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    fontSize: Sizes.SMALL,
    overflow: 'hidden',
    alignSelf: 'flex-end',
    textTransform: 'uppercase',
  },
  empty: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 40,
  },
  emptyText: {color: '#fff'},
  mainContainer: {flex: 1},
});

export default ChatListScreen;