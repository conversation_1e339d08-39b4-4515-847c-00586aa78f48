import React, {useCallback, useState, useEffect, useRef} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  Image,
  Alert,
} from 'react-native';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import EmptyMessages from './EmptyMessages';
import MessageListContainer from './MessageListContainer';
import LoaderOverlay from '../../../../components/common/LoaderOverlay';
import {messageTabData} from '../../../../utils/templates/TemplateConfig';
import CustomTab from '../../../../components/common/CustomTabs';
import ScreenLayout from '../../../../components/layout/ScreenLayout';
import TitleSection from '../../../../components/common/TitleSection';
import {
  AppStrings,
  AppCommonIcons,
  RouteNames,
} from '../../../../utils/constants/AppStrings';
import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {MessageService} from '../../../../utils/services/MessageService';
import {wp} from '../../../../utils/ResponsiveParams';
import {TimelineService} from '../../../../utils/services/TimelineService';
import {onValue, get, ref, off} from '@react-native-firebase/database';
import {getApp} from '@react-native-firebase/app';
import {GC_CUSTOMER_ID} from '../../../../utils/globals';
import {VehicleService} from '../../../../utils/services/VehicleService';
import NetInfo from '@react-native-community/netinfo';

const MessagesScreen = () => {
  const route = useRoute();
  const initialTab = (route.params as any)?.initialTab || 0;
  const monthNames = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec',
  ];

  const [activeTabIndex, setActiveTabIndex] = useState(initialTab);
  const [allMessageList, setAllMessageList] = useState<any[]>([]);
  const [serviceMessageList, setServiceMessageList] = useState<any[]>([]);
  const [systemMessageList, setSystemMessageList] = useState<any[]>([]);
  const [pendingMessageList, setPendingMessageList] = useState<any[]>([]);
  const [multiplePendingMessageList, setMultiplePendingMessageList] = useState<any[]>([]);
  const [allPendingMessageList, setAllPendingMessageList] = useState<any[]>([]);
  const [refreshKey, setRefreshKey] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
 const [dataProcessingStatus, setDataProcessingStatus] = useState({
    service: false,
    system: false,
    pending: false
  });

  const navigation = useNavigation<any>();
  const serviceListenerRef = useRef<any>(null);
  const systemListenerRef = useRef<any>(null);
  const pendingListenerRef = useRef<any>(null);
  const failedImagesRef = useRef<Record<string, boolean>>({});
  const [, forceRerender] = useState(0);
   useFocusEffect(
     useCallback(() => {
       const checkNetworkAndLoad = async () => {
         const state = await NetInfo.fetch();
         if (!state.isConnected) {
           navigation.navigate('NetworkFailedPage' as never);
           return;
         }

         // Get current route params
         const currentRoute = navigation
           .getState()
           ?.routes?.find((r: any) => r.name === RouteNames.MCX_NAV_MESSAGES);
         const routeParams = currentRoute?.params as any;
         const currentInitialTab = routeParams?.initialTab ?? initialTab;
         const shouldRefresh = routeParams?.refresh ?? false;
         const approvedAppointment = routeParams?.approvedAppointment;
         const cancelledAppointment = routeParams?.cancelledAppointment;

         setActiveTabIndex(currentInitialTab);
         setRefreshKey(prev => prev + 1); // Force complete re-render

         if (shouldRefresh) {
           setIsLoading(true);
           setServiceMessageList([]);
           setSystemMessageList([]);
           setPendingMessageList([]);
           setMultiplePendingMessageList([]);
           setAllMessageList([]);
           setAllPendingMessageList([]);
           if (approvedAppointment) {
             setServiceMessageList(prev => prev.filter(msg =>
               msg['work-request-id'] !== approvedAppointment
             ));
             setAllMessageList(prev => prev.filter(msg =>
               msg['work-request-id'] !== approvedAppointment
             ));
           }          
           if (cancelledAppointment) {
             setPendingMessageList(prev => prev.filter(msg =>
               msg['work-request-id'] !== cancelledAppointment
             ));
             setAllPendingMessageList(prev => prev.filter(msg =>
               msg['work-request-id'] !== cancelledAppointment
             ));
           }
           await refreshAllData();
         } else {
           if (currentInitialTab === 3) {
             setIsLoading(true);
             await refreshPendingData();
             setIsLoading(false);
           }
         }
       };

       checkNetworkAndLoad();
     }, [initialTab, navigation]),
   );

   useEffect(() => {

   }, [
     allMessageList,
     serviceMessageList,
     systemMessageList,
     allPendingMessageList,
   ]);

   // Initial data fetch on mount
   useEffect(() => {
     setupListeners();
     return () => {
       cleanupListeners();
     };
   }, []);

   useEffect(() => {
     const allPending = [...pendingMessageList, ...multiplePendingMessageList];
     const uniquePending = allPending.filter(
       (item, index, self) =>
         index ===
         self.findIndex(t => t['message-key'] === item['message-key']),
     );
     const sorted = sortMessages(uniquePending);
     setAllPendingMessageList(sorted);
   }, [pendingMessageList, multiplePendingMessageList]);
   useEffect(() => {
     const allDataProcessed = dataProcessingStatus.service &&
                              dataProcessingStatus.system &&
                              dataProcessingStatus.pending;
     
     if (allDataProcessed) {
       setIsLoading(false);
     }
   }, [dataProcessingStatus]);

   useEffect(() => {
     const merged = mergeMessages(systemMessageList, serviceMessageList);
     setAllMessageList(sortMessages(merged));
   }, [systemMessageList, serviceMessageList]);

 const setupListeners = async () => {
     try {
       setIsLoading(true);
       const serviceRef = await MessageService.getServiceMessages();
       serviceListenerRef.current = onValue(serviceRef, async serviceData => {
         try {        
           const serviceValues = serviceData.val();
           if (serviceValues && serviceData.numChildren() > 0) {          
             const result = await processServiceMessage(serviceValues);
             setServiceMessageList(sortMessages(result));
             setDataProcessingStatus(prev => ({ ...prev, service: true }));
           } else {
             setServiceMessageList([]);
             setDataProcessingStatus(prev => ({ ...prev, service: true }));
           }
         } catch (error) {
           console.error('📱 MessagesScreen: Error processing service messages:', error);
           setServiceMessageList([]);
           setDataProcessingStatus(prev => ({ ...prev, service: true }));
         }
       });

       const systemRef = await MessageService.getSystemMessages();
       systemListenerRef.current = onValue(systemRef, async systemData => {
         try {
           const systemValues = systemData.val();
           if (systemValues && systemData.numChildren() > 0) {
             const result = await processSystemMessage(systemValues);
             setSystemMessageList(sortMessages(result));
             setDataProcessingStatus(prev => ({ ...prev, system: true }));
           } else {
             setSystemMessageList([]);
             setDataProcessingStatus(prev => ({ ...prev, system: true }));
           }
         } catch (error) {
           setSystemMessageList([]);
           setDataProcessingStatus(prev => ({ ...prev, system: true }));
         }
       });


       const pendingRef = await MessageService.getPendingRequest();
       pendingListenerRef.current = onValue(pendingRef, async pendingData => {
         try {
           const pendingValues = pendingData.val();
           if (pendingValues && pendingData.numChildren() > 0) {
             const result = await processPendingRequest(pendingValues);
             setPendingMessageList(result);
             setDataProcessingStatus(prev => ({ ...prev, pending: true }));
           } else {
             setPendingMessageList([]);
             setDataProcessingStatus(prev => ({ ...prev, pending: true }));
           }
         } catch (error) {
           console.error('⏳ MessagesScreen: Error processing pending requests:', error);
           setPendingMessageList([]);
           setDataProcessingStatus(prev => ({ ...prev, pending: true }));
         }
       });

       // Fetch multiple pending requests
       await fetchMultiplePendingRequest();

     } catch (error) {
       console.error(' MessagesScreen: Error setting up listeners:', error);
       setIsLoading(false);
     }
   };

   const cleanupListeners = () => {
     if (serviceListenerRef.current) {
       try {
         serviceListenerRef.current();
       } catch (error) {
         console.warn('Error cleaning up service listener:', error);
       }
     }
     if (systemListenerRef.current) {
       try {
         systemListenerRef.current();
       } catch (error) {
         console.warn('Error cleaning up system listener:', error);
       }
     }
     if (pendingListenerRef.current) {
       try {
         pendingListenerRef.current();
       } catch (error) {
         console.warn('Error cleaning up pending listener:', error);
       }
     }
   };

   const refreshPendingData = async () => {
     setIsLoading(true);
     try {
       await fetchMultiplePendingRequest();
       const pendingRef = await MessageService.getPendingRequest();
       const pendingSnapshot = await get(pendingRef);
       const pendingValues = pendingSnapshot.val();     
       if (pendingValues) {
         const result = await processPendingRequest(pendingValues);
         setPendingMessageList(result);
       } else {
         setPendingMessageList([]);
       }
     } catch (error) {
       console.error(' MessagesScreen: Error refreshing pending data:', error); // Keep existing data on error, don't clear
     } finally {
       setIsLoading(false);
     }
   };

   const refreshAllData = async () => {
     setIsLoading(true);
     try {
       await fetchMultiplePendingRequest(); 

       const refreshTimeout = 10000;

       const refreshPromise = Promise.all([
         MessageService.getServiceMessages().then(ref => get(ref)),
         MessageService.getSystemMessages().then(ref => get(ref)),
         MessageService.getPendingRequest().then(ref => get(ref)),
       ]);

       const refreshResult = await Promise.race([
         refreshPromise,
         new Promise((_, reject) =>
           setTimeout(
             () => reject(new Error('Refresh timeout')),
             refreshTimeout,
           ),
         ),
       ]);

       const [serviceSnapshot, systemSnapshot, pendingSnapshot] =
         refreshResult as [any, any, any]; 

       try {
         const serviceValues = serviceSnapshot.val();
         if (serviceValues && serviceSnapshot.numChildren() > 0) {
           const result = await processServiceMessage(serviceValues);
           setServiceMessageList(sortMessages(result));
         } else {
           setServiceMessageList([]);
         }
       } catch (error) {
         console.error(
           'Error processing service messages during refresh:',
           error,
         );
         setServiceMessageList([]);
       } 

       try {
         const systemValues = systemSnapshot.val();
         if (systemValues && systemSnapshot.numChildren() > 0) {
           const result = await processSystemMessage(systemValues);
           setSystemMessageList(sortMessages(result));
         } else {
           setSystemMessageList([]);
         }
       } catch (error) {
         console.error(
           'Error processing system messages during refresh:',
           error,
         );
         setSystemMessageList([]);
       } 

       try {
         const pendingValues = pendingSnapshot.val();
         if (pendingValues && pendingSnapshot.numChildren() > 0) {
           const result = await processPendingRequest(pendingValues);
           setPendingMessageList(result);
         } else {
           setPendingMessageList([]);
         }
       } catch (error) {
         console.error(
           'Error processing pending messages during refresh:',
           error,
         );
         setPendingMessageList([]);
       }

       await fetchMultiplePendingRequest();
     } catch (error) {
       console.error('Error refreshing all data:', error); 
       if (
         error instanceof Error &&
         error.message.includes('Refresh timeout')
       ) {
         console.warn('Data refresh timed out, using cached data if available'); 
  
         console.warn('Refreshing data failed, showing empty state');
         setServiceMessageList([]);
         setSystemMessageList([]);
         setPendingMessageList([]);
       }
     } finally {
       setIsLoading(false);
     }
   };

   const fetchMultiplePendingRequest = async () => {
     try {
       const data = await VehicleService.getMultiplePendingRequest();
       if (!data) {
         setMultiplePendingMessageList([]);
         return;
       }

       const multipleReqArray: string[] = [];
       const multipleReqStatus: string[] = [];
       Object.keys(data).forEach(key => {
         const val = data[key];
         if (val.status === 'pending' || val.status === 'open') {
           multipleReqArray.push(key);
           multipleReqStatus.push(val.status);
         }
       });

       if (multipleReqArray.length > 0) {
         const result = await processMultiplePendingRequest(
           multipleReqArray,
           multipleReqStatus,
         );
         setMultiplePendingMessageList(result);
       } else {
         setMultiplePendingMessageList([]);
       }
     } catch (error) {
       console.error('Error fetching multiple pending request:', error);
       setMultiplePendingMessageList([]);
     }
   };

   const handleTabPress = async (index: number) => {
     setActiveTabIndex(index); 

     if (index === 3) {
       await refreshPendingData();
     } 
   };
  const processServiceMessage = async (serviceMessage: any): Promise<any[]> => {
    if (!serviceMessage) return [];
    
    const keys = Object.keys(serviceMessage);
    const tempPromises = keys.map(async key => {
      const msg = serviceMessage[key];
      
      if (!msg || (msg.type !== 'appointment' && msg.type !== 'session')) {
        return null;
      }

      try {
        const workRequestRef = await MessageService.fetchWorkRequest(msg['work-request-id']);
        const dataSnapshot = await get(workRequestRef);
        const data = dataSnapshot.val();
        
        if (!data || !data.services ) {
        return null;
      }

        const date = new Date(data['request-date']);
        date.setMinutes(date.getMinutes() + date.getTimezoneOffset());

        const services = await processServices(data.services);
        
        let mechanic = {};
        if (data.mechanic) {
          mechanic = await getMechanicDetails(data.mechanic);
        }

        const details = {
          services,
          time: data['request-time-range'],
          day: date.getDate(),
          month: monthNames[date.getMonth()],
          mechanic,
          'multiple-request': data['multiple-request-id'],
        };

        if (!msg['customer-delete'] && mechanic && Object.keys(mechanic).length > 0) {
          return {
            'message-key': key,
            'customer-read': msg['customer-read'] || false,
            types: 'services',
            'work-request-id': msg['work-request-id'],
            'is-approved': data.isApproved,
            'is-paid': data.paymentStatus,
            'created-time': msg['created-time'],
            details,
            'work-status': data.status,
            status: msg.type,
            'mechanic-id': data.mechanic,
          };
        }
      } catch (error) {
        console.error('Error processing service message:', error);
        // Handle permission denied specifically
        if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string' && error.message.includes('permission-denied')) {
          console.log(`Permission denied for service message ${key}, skipping`);
        }
      }
      return null;
    });

    const results = await Promise.all(tempPromises);
    return results.filter(Boolean);
  };

  const processSystemMessage = async (systemMessage: any): Promise<any[]> => {
    const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
    if (!customerId) return [];

    const keys = Object.keys(systemMessage);
    const temp: any[] = [];

    keys.forEach(key => {
      const msg = systemMessage[key];
      const isDeleted = msg.delete && msg.delete[customerId];
      const isRead = msg.read && msg.read[customerId];

      if (!isDeleted) {
        const createdTime = msg['created-time'];
        const date = new Date(createdTime);
        date.setMinutes(date.getMinutes() + date.getTimezoneOffset());

        temp.push({
          'message-key': key,
          read: !!isRead,
          types: 'systems',
          'created-time': createdTime,
          details: {
            title: msg.title,
            description: msg.description,
            time: msg['time-range'],
            day: date.getDate(),
            month: monthNames[date.getMonth()],
          },
        });
      }
    });

    return temp;
  };

  const processServices = async (services: any): Promise<any[]> => {
    if (!services) return [];
    
    const serId = Object.keys(services);
    const servicePromises = serId.map(async svId => {
      try {
        let tempSubser = services[svId]['sub-services'] || [];
        if (!Array.isArray(tempSubser)) tempSubser = [];

        const subServicePromises = tempSubser.map(async (ssv: string) => {
          try {
            const subServiceRef = VehicleService.fetchSubServiceName(ssv);
            const subServiceSnapshot = await get(subServiceRef);
            const subServiceData = subServiceSnapshot.val();
            return subServiceData?.[ssv]?.name || '';
          } catch {
            return '';
          }
        });

        const subServiceNames = await Promise.all(subServicePromises);

        const serviceRef = VehicleService.fetchServiceName(svId);
        const serviceSnapshot = await get(serviceRef);
        const serviceData = serviceSnapshot.val();
        
        return {
          service: serviceData?.[svId]?.name || '',
          image: 'assets/imgs/oil-icon.jpg',
          subservice: subServiceNames.filter(Boolean),
        };
      } catch {
        return null;
      }
    });

    const results = await Promise.all(servicePromises);
    return results.filter(Boolean);
  };

  const getMechanicDetails = async (mechanicId: string): Promise<any> => {
    if (!mechanicId) return {};

    try {
      const mechanicRef = VehicleService.getMechanicDetail(mechanicId);
      const mechanicSnapshot = await get(mechanicRef);
      const mechanicData = mechanicSnapshot.val();
      
      // Return the mechanic data or empty object
      return mechanicData || {};
    } catch (error) {
      console.error('Error fetching mechanic details:', error);
      return {};
    }
  };

  const processPendingRequest = async (pendingValues: any): Promise<any[]> => {
    const keys = Object.keys(pendingValues);

    // Process in batches to avoid excessive pending callbacks
    const BATCH_SIZE = 10;
    const results: any[] = [];

    for (let i = 0; i < keys.length; i += BATCH_SIZE) {
      const batch = keys.slice(i, i + BATCH_SIZE);
      const batchPromises = batch.map(async key => {
        const workRequestData = pendingValues[key];
        const status = workRequestData.status?.toLowerCase();

        if  (status !== 'pending') {
          return null;
        }

        try {
          const workRequestRef = await MessageService.fetchWorkRequest(key);
          const dataSnapshot = await get(workRequestRef);
          const data = dataSnapshot.val();

          // Handle permission denied or null data
          if (!data) {
            return null;
          }

          // Handle different data structures
          const workRequest = data[key] || data;

          if (!workRequest) {;
            return null;
          }

          // Handle date with validation
          let date;
          try {
            const requestDate = workRequest['request-date'];
            if (!requestDate) {           
              return null;
            }

            date = new Date(requestDate);
            if (isNaN(date.getTime())) {
              console.warn(`Invalid date for ${key}: ${requestDate}`);
              return null;
            }

            date.setMinutes(date.getMinutes() + date.getTimezoneOffset());
          } catch (dateError) {
            console.error(`Date error for ${key}:`, dateError);
            return null;
          }
          let mechanicDetails = {};
          let mechanicId = workRequest.mechanic;

          try {

            if (mechanicId) {
              mechanicDetails = await getMechanicDetails(mechanicId);
            }

            if (!mechanicDetails || Object.keys(mechanicDetails).length === 0) {
              try {
                const timeline = await TimelineService.loadTimeline(key);

                if (Array.isArray(timeline) && timeline.length > 0) {
 
                  const entryWithMechanic = timeline.find((t: any) => {
                    if (!t.mechanic) return false;

                    if (typeof t.mechanic === 'object' && Object.keys(t.mechanic).length > 0) {
                      return true;
                    }
                    if (typeof t.mechanic === 'string' && t.mechanic.trim() !== '') {
                      return true;
                    }

                    return false;
                  });

                  if (entryWithMechanic) {

                    if (typeof entryWithMechanic.mechanic === 'string') {
                      mechanicId = entryWithMechanic.mechanic;
                      mechanicDetails = await getMechanicDetails(entryWithMechanic.mechanic);   
                    } else if (typeof entryWithMechanic.mechanic === 'object') {
                      mechanicDetails = entryWithMechanic.mechanic;
                      mechanicId = entryWithMechanic.mechanic.id || mechanicId;      
                    }
                  } else {
                    console.log(`No mechanic found in timeline for ${key}`);
                  }
                } else {
                  console.log(`Empty or invalid timeline for ${key}`);
                }
              } catch (tlError) {
                console.warn(`Timeline lookup failed for ${key}:`, tlError);
              }
            }
          } catch (mError) {
            console.error(`Error fetching mechanic for ${key}:`, mError);
            mechanicDetails = {};
          }

          // Process services
          const processedServices = await processServices(workRequest.services || {});

          // Validate mechanic data - must have first and last name
          const validMechanicData = mechanicDetails &&
            Object.keys(mechanicDetails).length > 0 &&
            (mechanicDetails as any)['first-name'] &&
            (mechanicDetails as any)['last-name']
              ? mechanicDetails
              : {};
          const details = {
            services: processedServices,
            time: workRequest['request-time-range'] || '',
            day: date.getDate(),
            month: monthNames[date.getMonth()],
            mechanic: validMechanicData,
          };

          const result = {
            'message-key': key,
            'customer-read': false,
            types: 'request',
            'work-request-id': key,
            'is-approved': workRequest.isApproved || false,
            'is-paid': workRequest.paymentStatus || false,
            'created-time': workRequest['created-time'] || Date.now(),
            details,
            'work-status': workRequest.status || status,
            status,
            'mechanic-id': mechanicId || null,
          };   
          return result;
        } catch (error) {
          console.error(`Error processing pending request ${key}:`, error);
          // Handle permission denied specifically
          if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string' && error.message.includes('permission-denied')) {           
            return null;
          }
          return null;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults.filter(Boolean));

      // Add a small delay between batches to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 50));
    }
    const filtered = results.filter(Boolean);
    return filtered;
  };

  const processMultiplePendingRequest = async (
    multipleReqArray: string[],
    multipleReqStatus: string[],
  ): Promise<any[]> => {
    const tempPromises = multipleReqArray.map(async (workRequestId, index) => {
      const status = multipleReqStatus[index]?.toLowerCase();

      if (status !== 'pending' && status !== 'open') return null;

      try {
        const workRequestRef = ref(
          getApp().database(),
          `multiple-request-data/${workRequestId}`,
        );
        const dataSnapshot = await get(workRequestRef);
        const workRequestData = dataSnapshot.val();

        if (!workRequestData) return null;

        // Handle date
        let date;
        try {
          const requestDate = workRequestData['request-date'];
          if (!requestDate) return null;
          
          date = new Date(requestDate);
          if (isNaN(date.getTime())) return null;
          
          date.setMinutes(date.getMinutes() + date.getTimezoneOffset());
        } catch {
          return null;
        }

        // CRITICAL FIX: Enhanced mechanic fetching for multiple requests
        let mechanicDetails: any = {};
        let mechanicId = workRequestData.mechanic;
        
        try {
          // Step 1: Try explicit mechanic ID
          if (mechanicId) {
            mechanicDetails = await getMechanicDetails(mechanicId);       
          }
          
          // Step 2: Try timeline if no mechanic found
          if (!mechanicDetails || Object.keys(mechanicDetails).length === 0) {
            try {
              const timeline = await TimelineService.loadTimeline(workRequestId);
              if (Array.isArray(timeline) && timeline.length > 0) {
                const entryWithMechanic = timeline.find((t: any) => {
                  if (!t.mechanic) return false;
                  
                  if (typeof t.mechanic === 'object' && Object.keys(t.mechanic).length > 0) {
                    return true;
                  }
                  
                  if (typeof t.mechanic === 'string' && t.mechanic.trim() !== '') {
                    return true;
                  }
                  
                  return false;
                });
                
                if (entryWithMechanic) {
                  if (typeof entryWithMechanic.mechanic === 'string') {
                    mechanicId = entryWithMechanic.mechanic;
                    mechanicDetails = await getMechanicDetails(entryWithMechanic.mechanic);    
                  } else {
                    mechanicDetails = entryWithMechanic.mechanic || {};
                    mechanicId = entryWithMechanic.mechanic.id || mechanicId;                   
                  }
                }
              }
            } catch (tlErr) {
              console.warn(`Timeline lookup failed for multiple ${workRequestId}:`, tlErr);
            }
          }
        } catch (mErr) {
          console.warn(`Failed to resolve mechanic for multiple ${workRequestId}:`, mErr);
        }

        const validMechanicData = mechanicDetails && 
          Object.keys(mechanicDetails).length > 0 &&
          (mechanicDetails as any)['first-name'] && 
          (mechanicDetails as any)['last-name'] 
            ? mechanicDetails 
            : {};

        const details = {
          services: await processServices(workRequestData.services),
          time: workRequestData['request-time-range'],
          day: date.getDate(),
          month: monthNames[date.getMonth()],
          mechanic: validMechanicData,
        };

        return {
          'message-key': workRequestId,
          'customer-read': false,
          types: 'request',
          'work-request-id': workRequestId,
          'is-approved': workRequestData.isApproved,
          'is-paid': workRequestData.paymentStatus,
          'created-time': workRequestData['created-time'],
          details,
          'work-status': status,
          status,
          'request-type': 'multiple',
          'mechanic-id': mechanicId || null,
        };
      } catch (error) {
        console.error('Error in processMultiplePendingRequest:', workRequestId, error);
        // Handle permission denied specifically
        if (error && typeof error === 'object' && 'message' in error && typeof error.message === 'string' && error.message.includes('permission-denied')) {
          return null;
        }
        return null;
      }
    });

    const results = await Promise.all(tempPromises);
    return results.filter(Boolean);
  };

  const sortMessages = (messages: any[]) => {
    return messages.sort((a, b) => b['created-time'] - a['created-time']);
  };

  const mergeMessages = (systemMessages: any[], serviceMessages: any[]) => {
    return [...systemMessages, ...serviceMessages];
  };

  const formatTimeRange = (timeRange: string) => {
    if (!timeRange) return '';
    
    const [start, end] = timeRange.split('-').map(t => t.trim());
    
    const formatTime = (time: string) => {
      const [hours, minutes] = time.split(':');
      const hour = parseInt(hours, 10);
      const formattedHour = hour % 12 || 12;
      return `${formattedHour}:${minutes}`;
    };

    return `${formatTime(start)} - ${formatTime(end)}`;
  };

  const goToMessageDetails = async (messageArray: any[], position: number) => {
    try {
      let timeline: any[] = [];
      const message = messageArray[position];

      if (message.types === 'services') {
        MessageService.readServiceMessage(message['message-key']);
        timeline = await TimelineService.loadTimeline(message['work-request-id']);
      } else if (message.types === 'systems') {
        MessageService.readSystemMessage(message['message-key']);
      } else if (message.types === 'request') {
        try {
          timeline = await TimelineService.loadTimeline(message['work-request-id']);
        } catch (error) {
          console.error('Error loading timeline:', error);
          timeline = [];
        }
      }
      navigation.navigate(RouteNames.MCX_NAV_MESSAGE_DETAILS as never, {
        'message-array': messageArray,
        'position': position,
        timeline,
        'initialTab': activeTabIndex,
      });   
    } catch (error) {
      console.error('Error in goToMessageDetails:', error);
    }
  };

  const deleteAllList = async (messageList: any[]) => {
    for (const message of messageList) {
      if (message.types === 'services') {
        await MessageService.deleteServiceMessage(message['message-key']);
      } else if (message.types === 'systems') {
        await MessageService.deleteSystemMessage(message['message-key']);
      }
    }
  };

  const doYouWantToDeleteMessage = (messageList: any[]) => {
    Alert.alert(
      'Delete message',
      'Are you sure you want to delete all messages? Note: There is no Undo.',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'DELETE',
          onPress: () => deleteAllList(messageList),
          style: 'destructive',
        },
      ],
    );
  };

const renderMessageItem = (message: any, index: number, messageArray: any[]) => {
  const isRead = message['customer-read'] || message.read;
  let statusText = '';
  let statusStyle = {};
  let title = '';
  let subtitle = '';
  let services: any[] = [];
  let headerIcon = AppCommonIcons.MCX_SETTINGS_ICON;

    if (message.types === 'services' || message.types === 'request') {
      services = message.details?.services || [];
      headerIcon = AppCommonIcons.MCX_OIL_ICON;

      if (message.types === 'services') {
        if (
          message['is-approved'] &&
          message['work-status'] !== 'cancelled' &&
          !message['is-paid']
        ) {
          subtitle = message.details?.time
            ? formatTimeRange(message.details.time)
            : '';
        }

        if (!message['is-approved'] && message['work-status'] !== 'cancelled') {
          statusText = 'Waiting for your confirmation';
          statusStyle = styles.statusWaiting;
        } else if (
          !message['is-approved'] &&
          message['work-status'] === 'cancelled'
        ) {
          statusText = 'Cancelled';
          statusStyle = styles.statusCancelled;
        } else if (
          message['is-approved'] &&
          message['work-status'] !== 'cancelled' &&
          message['is-paid']
        ) {
          statusText = 'Completed';
          statusStyle = styles.statusCompleted;
        } else if (
          message['is-approved'] &&
          message['work-status'] === 'cancelled' &&
          message['is-paid']
        ) {
          statusText = 'Cancelled with cancellation fee';
          statusStyle = styles.statusCancelled;
        } else if (
          message['is-approved'] &&
          message['work-status'] === 'cancelled' &&
          !message['is-paid']
        ) {
          statusText = 'Cancelled';
          statusStyle = styles.statusCancelled;
        }
      } else if (message.types === 'request') {
        subtitle = message.details?.time
          ? formatTimeRange(message.details.time)
          : '';

        const status = message.status?.toLowerCase();
        statusText = 'Pending';
        statusStyle = styles.pstatusPending;
        switch (status) {
          case 'pending':
            statusText = 'Pending';
            statusStyle = styles.pstatusPending;
            break;
          case 'open':
            statusText = 'Pending'; // Add this case
            statusStyle = styles.pstatusPending; // Or a new style for 'open'
            break;
          case 'declined':
            statusText = 'Declined';
            statusStyle = styles.pstatusDeclined;
            break;
          case 'expired':
            statusText = 'Expired';
            statusStyle = styles.pstatusExpired;
            break;
          default:
            statusText = status;
            statusStyle = styles.pstatusPending;
        }
      }
    } else if (message.types === 'systems') {
      title = message.details?.title || 'System Message';
      subtitle = message.details?.time || message.details?.description || '';
      headerIcon = AppCommonIcons.MCX_SETTINGS_ICON;
      statusText = isRead ? 'Read' : 'Unread';
      statusStyle = isRead ? styles.readStatus : styles.unreadStatus;
    }

    // Skip rendering if this is a pending request and we have permission denied errors
    if (message.types === 'request' && message.status === 'open' && !message.details?.mechanic) {
    return null;
  }

  return (
    <TouchableOpacity
      onPress={() => goToMessageDetails(messageArray, index)}
      style={[styles.messageContainer, !isRead && message.types !== 'request' && styles.unreadMessage]}
      activeOpacity={0.8}>
      {!isRead && message.types !== 'request' && <View style={styles.unreadIndicator} />}
      <View style={styles.leftIconBlock}>
        <Image
          source={AppCommonIcons.MCX_FIND_MECHANIC_ICON}
          style={styles.leftIcon}
          resizeMode="contain"
          fadeDuration={0}
        />
      </View>
        <View style={styles.dateCard}>
          <Text style={styles.dateNumber}>{message.details?.day || '1'}</Text>
          <Text style={styles.dateMonth}>
            {message.details?.month || 'Jan'}
          </Text>
        </View>
        <View style={styles.messageContent}>
          <View style={styles.messageMainContent}>
            <View style={styles.messageInfo}>
              {services.length > 0 ? (
                <>
                  {services.map((service, idx) => (
                    <View key={idx} style={styles.serviceItem}>
                      <View style={styles.serviceHeader}>
                        <Image
                          source={AppCommonIcons.MCX_OIL_ICON}
                          style={styles.serviceIcon}
                        />
                        <View style={styles.serviceTextContainer}>
                          <Text
                            style={styles.messageTitle}
                            numberOfLines={1}
                            ellipsizeMode="tail">
                            {service?.service || 'Service'}
                          </Text>
                          {service?.subservice &&
                            service.subservice.length > 0 && (
                              <View style={styles.subserviceContainer}>
                                {service.subservice.map(
                                  (sub: string, subIdx: number) => (
                                    <Text
                                      key={subIdx}
                                      style={styles.messageSubservice}
                                      numberOfLines={1}
                                      ellipsizeMode="tail">
                                      {sub}
                                    </Text>
                                  ),
                                )}
                              </View>
                            )}
                        </View>
                      </View>
                    </View>
                  ))}
                  {subtitle && (
                    <View style={styles.timeContainer}>
                      <Text
                        style={styles.messageSubtitle}
                        numberOfLines={1}
                        ellipsizeMode="tail">
                        {subtitle}
                      </Text>
                    </View>
                  )}
                </>
              ) : (
                <View style={styles.messageHeader}>
                  {(() => {
                    const messageKey = message['message-key'] || `mk-${index}`;
                    const failed = !!failedImagesRef.current[messageKey];
                    const iconSource = failed ? AppCommonIcons.MCX_FIND_MECHANIC_ICON : headerIcon;
                    return (
                      <Image
                        source={iconSource}
                        style={styles.messageIcon}
                        resizeMode="contain"
                        fadeDuration={0}
                        onError={() => {
                          failedImagesRef.current[messageKey] = true;
                          forceRerender(n => n + 1);
                        }}
                        defaultSource={AppCommonIcons.MCX_FIND_MECHANIC_ICON}
                      />
                    );
                  })()}
                  <View style={styles.messageTextContainer}>
                    <Text
                      style={styles.messageTitle}
                      numberOfLines={2}
                      ellipsizeMode="tail">
                      {title}
                    </Text>
                    {subtitle && (
                      <Text
                        style={styles.messageSubtitle}
                        numberOfLines={1}
                        ellipsizeMode="tail">
                        {subtitle}
                      </Text>
                    )}
                  </View>
                </View>
              )}
            </View>
          </View>
          {statusText && (
            <View style={[styles.statusContainer, statusStyle]}>
              <Text style={styles.statusText}>{statusText}</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const renderTabs = () => (
    <View style={styles.tabsContainer}>
      {Object.entries(messageTabData).map(([key, label], idx) => (
        <CustomTab
          key={key}
          label={label}
          active={activeTabIndex === idx}
          onPress={() => handleTabPress(idx)}
        />
      ))}
    </View>
  );

const renderTabContent = () => {
  let content;
  let currentList;

  switch (activeTabIndex) {
    case 0:
      currentList = allMessageList;
      break;
    case 1:
      currentList = serviceMessageList;
      break;
    case 2:
      currentList = systemMessageList;
      break;
    case 3:
      currentList = allPendingMessageList;
      break;
    default:
      currentList = [];
  }

  const showHeader = activeTabIndex !== 3 && currentList.length > 0;

  content = (
    <MessageListContainer
      refreshKey={refreshKey} // ADD THIS PROP
      items={currentList}
      renderItem={renderMessageItem}
      headerComponent={
        showHeader ? (
          <View style={styles.headerRow}>
            <View style={styles.headerLeft}>
              <Text style={styles.headerText}>Types</Text>
            </View>
            <View style={styles.headerCenter}>
              <Text style={styles.headerText}>Details</Text>
            </View>
            <View style={styles.headerRight}>
              <TouchableOpacity
                style={styles.deleteAllButton}
                onPress={() => doYouWantToDeleteMessage(currentList)}>
                <Image
                  source={AppCommonIcons.MCX_DELETE_ICON}
                  style={styles.deleteIcon}
                />
              </TouchableOpacity>
            </View>
          </View>
        ) : null
      }
    />
  );

  return <View style={{paddingHorizontal: wp(2)}}>{content}</View>;
};

  return (
    <View style={{flex: 1, backgroundColor: Colors.APPBAR_BG_COLOR}}>
      <TitleSection
        title={AppStrings.MCX_MESSAGES_TITLE}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />
      <View style={styles.container}>
        {renderTabs()}
        <View style={styles.contentContainer}>
       <ScrollView 
          key={`scroll-${refreshKey}`} // ADD THIS KEY
          showsVerticalScrollIndicator={false}
        >
         {renderTabContent()}
        </ScrollView>
        </View>
      </View>
      <LoaderOverlay visible={isLoading} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  titleSection: {
    marginBottom: 12,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.15,
    shadowRadius: 8,
  },
  contentContainer: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingBottom: 8,
    paddingTop: 8,
    paddingHorizontal: 12,
  },
  messageContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.15,
    shadowRadius: 16,
     position: 'relative',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    justifyContent: 'space-around',
    marginTop: 10,
    borderRadius: 16,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.12,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    overflow: 'hidden',
  },
  messageContent: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'transparent',
  },
  messageMainContent: {
    flexDirection: 'row',
    backgroundColor: 'transparent',
    paddingVertical: 12,
  },
  messageHeader: {
    width: 60,
    alignItems: 'center',
    justifyContent: 'flex-start',
    paddingTop: 8,
    backgroundColor: '#F8F9FA',
    borderRightWidth: 1,
    borderRightColor: '#E9ECEF',
  },
  messageInfo: {
    flex: 1,
    paddingLeft: 12,
    paddingRight: 8,
    paddingVertical: 4,
    justifyContent: 'flex-start',
    backgroundColor: 'transparent',
  },
  serviceItem: {
    marginBottom: 8,
  },
  messageTitle: {
    fontSize: 15,
    fontWeight: '700',
    color: '#000',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 2,
    lineHeight: 18,
  },
  messageSubtitle: {
    fontSize: 13,
    color: '#6B7280',
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
    marginTop: 4,
    lineHeight: 16,
  },
  messageSubservice: {
    fontSize: 12,
    color: Colors.PRIMARY,
    fontFamily: Fonts.ROBO_REGULAR,
    opacity: 0.9,
    marginTop: 1,
    lineHeight: 15,
  },
  pstatusPending: {
    backgroundColor: '#FFC107',
  },
  pstatusDeclined: {
    backgroundColor: '#DC3545',
  },
  pstatusExpired: {
    backgroundColor: '#6C757D',
  },
  statusContainer: {
    alignSelf: 'stretch',
    paddingHorizontal: 12,
    paddingVertical: 8,
    justifyContent: 'center',
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_REGULAR,
    textAlign: 'center',
    textTransform: 'none',
  },
  leftIconBlock: {
    width: 70,
    alignSelf: 'stretch',
    backgroundColor: Colors.PRIMARY_DARK,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
  },
  leftIcon: {
    width: 36,
    height: 36,
    tintColor: Colors.COMMON_WHITE_SHADE,
  },
  dateCard: {
    width: 70,
    alignSelf: 'stretch',
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    borderRightWidth: 1,
    borderRightColor: 'rgba(255, 255, 255, 0.2)',
  },
  dateNumber: {
    fontSize: 28,
    fontWeight: '800',
    color: Colors.COMMON_WHITE_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
    lineHeight: 32,
  },
  dateMonth: {
    fontSize: 12,
    color: Colors.COMMON_WHITE_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '600',
    textTransform: 'uppercase',
    marginTop: 2,
  },
  messageIcon: {
    width: 40,
    height: 40,
    resizeMode: 'contain',
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  serviceIcon: {
    width: 40,
    height: 40,
    marginRight: 8,
    resizeMode: 'contain',
  },
  serviceTextContainer: {
    flex: 1,
  },
  messageTextContainer: {
    flex: 1,
    paddingLeft: 12,
  },
  subserviceContainer: {
    marginTop: 4,
  },
  unreadStatus: {
    backgroundColor: '#DC3545',
  },
  readStatus: {
    backgroundColor: '#28A745',
  },
  statusWaiting: {
    backgroundColor: '#f01f34ff',
  },
  statusCancelled: {
    backgroundColor: '#ec3d11ff',
  },
  statusCompleted: {
    backgroundColor: '#28A745',
  },
  headerRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: wp(2),
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  headerLeft: {
    flex: 1,
  },
  headerCenter: {
    flex: 3,
  },
  headerRight: {
    width: 44,
    alignItems: 'flex-end',
  },
  headerText: {
    fontSize: Sizes.LARGE,
    color: Colors.COMMON_BlACK_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '700',
  },
  deleteAllButton: {
    padding: 8,
  },
  deleteIcon: {
    width: 24,
    height: 24,
    tintColor: '#DC3545',
  },
  timeContainer: {
    alignItems: 'flex-end',
    marginTop: 8,
  },
  unreadMessage: {
  backgroundColor: 'rgba(248, 249, 250, 0.98)', // CHANGE THIS
},
unreadIndicator: {
  position: 'absolute',
  right: 0,
  top: 0,
  bottom: 0,
  width: 5,
  backgroundColor: '#6B7280',
  zIndex: 10,
  borderTopRightRadius: 10,
  borderBottomRightRadius: 10,
},
});

export default MessagesScreen;