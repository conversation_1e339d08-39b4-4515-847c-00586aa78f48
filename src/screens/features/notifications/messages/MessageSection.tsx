import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors, Sizes } from '../../../../utils/constants/Theme';
import HorizontalDivider from '../../../../components/common/HorizontalDivider';

interface MessageSectionProps {
  sections: string[];
  onDeletePress?: () => void;
}

const MessageSection: React.FC<MessageSectionProps> = ({
  sections,
  onDeletePress
}) => {
  return (
    <View style={styles.mainContainer}>
      <View style={styles.container}>
        <View style={styles.sectionsContainer}>
          {sections.map((section, index) => (
            <Text key={index} style={styles.sectionText}>{section}</Text>
          ))}
        </View>
        {onDeletePress && (
          <TouchableOpacity onPress={onDeletePress} style={styles.deleteButton}>
            <Icon name="delete-outline" size={24} color="#B00020" />
          </TouchableOpacity>
        )}
      </View>
      <View style={styles.line}>
        <HorizontalDivider height={2} color={Colors.COMMON_BlACK_SHADE} />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    marginTop: 10,
  },
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  sectionsContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  sectionText: {
    fontSize: Sizes.LARGE,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    fontWeight: '600',
    marginRight: 24,
  },
  line: {
    marginTop: 10,
  },
  deleteButton: {
    padding: 4,
  },
});

export default MessageSection;