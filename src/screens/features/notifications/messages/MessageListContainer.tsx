import React from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import EmptyMessages from './EmptyMessages';
import { Colors, Fonts, Sizes } from '../../../../utils/constants/Theme';
import { wp } from '../../../../utils/ResponsiveParams';

interface Props {
  items?: any[];
  renderItem: (message: any, index: number, array: any[]) => React.ReactElement | null;
  onDeleteAll?: () => void;
  headerComponent?: React.ReactNode;
  refreshKey?: number; // Add this prop to force refresh
}

const MessageListContainer: React.FC<Props> = ({ 
  items = [], 
  renderItem, 
  headerComponent,
  refreshKey = 0 
}) => {
  const hasData = Array.isArray(items) && items.length > 0;

  // Use FlatList for better performance and proper key management
  const renderFlatListItem = ({ item, index }: { item: any; index: number }): React.ReactElement | null => {
    return renderItem(item, index, items);
  };

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        {headerComponent ? (
          headerComponent
        ) : (
          <View style={styles.headerRow}>
            <View style={styles.headerLeft}>
              <Text style={styles.headerText}>Types</Text>
            </View>
            <View style={styles.headerCenter}>
              <Text style={styles.headerText}>Details</Text>
            </View>
          </View>
        )}

        <View style={styles.divider} />

        <View style={styles.contentContainer}>
          {hasData ? (
            <FlatList
              key={`message-list-${refreshKey}`} // Force refresh when key changes
              data={items}
              renderItem={renderFlatListItem}
              keyExtractor={(item, index) => item['message-key'] || `msg-${index}`}
              scrollEnabled={false} // Disable internal scrolling since parent has ScrollView
              removeClippedSubviews={false} // Ensure images stay loaded
              initialNumToRender={10}
              maxToRenderPerBatch={10}
              windowSize={10}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listWrap}
            />
          ) : (
            <View style={styles.emptyWrap}>
              <EmptyMessages />
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 8,
  },
  card: {
    backgroundColor: '#ffffff',
    marginHorizontal: wp(2),
    borderRadius: 2,
    overflow: 'hidden',
  },
  headerRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: wp(2),
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  headerLeft: {
    flex: 1,
  },
  headerCenter: {
    flex: 3,
  },
  headerRight: {
    width: 44,
    alignItems: 'flex-end',
  },
  headerText: {
    fontSize: Sizes.LARGE,
    color: Colors.COMMON_BlACK_SHADE,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '700',
  },
  deleteIcon: {
    width: 26,
    height: 26,
    tintColor: Colors.PRIMARY,
  },
  divider: {
    height: 2,
    backgroundColor: Colors.COMMON_GREY_SHADOW_LIGHT_COLOR,
    marginHorizontal: wp(2),
  },
  contentContainer: {
    paddingVertical: 12,
  },
  listWrap: {
    paddingHorizontal: wp(2),
  },
  emptyWrap: {
    minHeight: 120,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 24,
  },
});

export default MessageListContainer;