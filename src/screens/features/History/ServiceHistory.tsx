import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    Image,
    StyleSheet,
    FlatList,
    TouchableOpacity,
    ActivityIndicator,
    SafeAreaView,
    Alert,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation } from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import NetInfo from '@react-native-community/netinfo';
import auth from '@react-native-firebase/auth';
import { getDatabase, ref, get, query, orderByChild, equalTo } from '@react-native-firebase/database';
import AppBackground from '../../../components/ui/AppBackground';
import { AppCommonIcons } from '../../../utils/constants/AppStrings';

// Month names array
const MONTH_NAMES = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];

// Interface definitions
interface SubService {
    name: string;
}

interface ServiceDetail {
    service: string;
    image: string;
    subservice: string[];
}

interface CompletedAppointmentDetails {
    services: ServiceDetail[];
    time: string;
    day: string;
    month: string;
    mechanic: string;
    'mechanic-name': string;
    'mechanic-rate': number;
    'mechanic-img': string;
    'mechanic-address': string;
    'mechanic-availability': boolean;
}

interface CompletedAppointment {
    'work-request-id': string;
    'created-time': number;
    details: CompletedAppointmentDetails;
}

// ProfileCard Component
const ProfileCard: React.FC<{
    imageUrl: string;
    name: string;
    address: string;
    rating: number;
}> = ({ imageUrl, name, address, rating }) => (
    <View style={styles.profileCard}>
        <Image
            source={imageUrl ? { uri: imageUrl } : AppCommonIcons.MCX_USER_PROFILE_PIC}
            style={styles.profileImage}
        />
        <View style={{ flex: 1 }}>
            <Text style={styles.profileName}>{name}</Text>
            <Text style={styles.profileAddress}>{address}</Text>
            <View style={styles.ratingContainer}>
                <Icon name="star" size={16} color="#e7b7b7ff" />
                <Text style={styles.ratingText}> {rating ? rating.toFixed(1) : '0.0'}</Text>
            </View>
        </View>
    </View>
);

// ServicesMessage Component
const ServicesMessage: React.FC<{
    day: string;
    month: string;
    services: ServiceDetail[];
    time: string;
}> = ({ day, month, services, time }) => {
    const getServiceNames = () => {
        if (!services || services.length === 0) return 'No services';
        
        return services.map(service => {
            if (service.subservice && service.subservice.length > 0) {
                return service.subservice.join(', ');
            }
            return service.service;
        }).join(', ');
    };

    return (
        <View style={styles.serviceMessage}>
            <View style={styles.serviceDateContainer}>
                <Text style={styles.serviceDateDay}>{day}</Text>
                <Text style={styles.serviceDateMonth}>{month}</Text>
            </View>
            <View style={styles.serviceDetailsContainer}>
                <Text style={styles.serviceTitle}>{getServiceNames()}</Text>
                <Text style={styles.serviceTime}>{time}</Text>
            </View>
        </View>
    );
};

const ServiceHistoryScreen: React.FC = () => {
    const navigation = useNavigation();
    const insets = useSafeAreaInsets();
    
    const [completedAppointmentArray, setCompletedAppointmentArray] = useState<CompletedAppointment[]>([]);
    const [loaderHistoryService, setLoaderHistoryService] = useState<boolean>(false);
    const [customerId, setCustomerId] = useState<string>('');

    useEffect(() => {
        checkNetworkAndAuthentication();
    }, []);

    /**
     * Check network connectivity and user authentication
     */
    const checkNetworkAndAuthentication = async () => {
        try {
            // Check network connectivity
            const state = await NetInfo.fetch();
            if (!state.isConnected) {
                console.log("Network check failed: ", state.isConnected);
                navigation.navigate('NetworkFailedPage' as never);
                return;
            }

            // Check if user exists
            const user = auth().currentUser;
            if (!user) {
                console.log("User not authenticated");
                navigation.navigate('LoginMainScreen' as never);
                return;
            }

            setCustomerId(user.uid);
            
            // Load completed sessions
            getCompletedSessions(user.uid);

        } catch (error) {
            console.error('Error in network/auth check:', error);
            Alert.alert('Error', 'Failed to initialize. Please try again.');
        }
    };

    /**
     * Fetch all completed sessions
     */
    const getCompletedSessions = async (userId: string) => {
        setLoaderHistoryService(false);
        console.log('getCompletedSessions called');

        try {
            const db = getDatabase();
            const sessionsRef = ref(db, `customer/${userId}/sessions`);
            const sessionsQuery = query(sessionsRef, orderByChild('status'), equalTo('completed'));

            const snapshot = await get(sessionsQuery);
            const allAcceptedData = snapshot.val();
            
            console.log('allAcceptedData:', allAcceptedData);

            if (!allAcceptedData) {
                setLoaderHistoryService(true);
                return;
            }

            const keys = Object.keys(allAcceptedData);
            console.log('Session keys:', keys);

            const appointmentsPromises = keys.map(sessionKey => 
                processSession(sessionKey)
            );

            const appointments = await Promise.all(appointmentsPromises);
            
            // Filter out null values and sort by created time (newest first)
            const validAppointments = appointments
                .filter(apt => apt !== null)
                .sort((a, b) => b!['created-time'] - a!['created-time']);

            setCompletedAppointmentArray(validAppointments as CompletedAppointment[]);
            setLoaderHistoryService(true);

        } catch (error) {
            console.error('Error fetching completed sessions:', error);
            setLoaderHistoryService(true);
            Alert.alert('Error', 'Failed to load service history');
        }
    };

    /**
     * Process individual session
     */
    const processSession = async (sessionKey: string): Promise<CompletedAppointment | null> => {
        try {
            const db = getDatabase();

            // Fetch session details
            const sessionRef = ref(db, `session/${sessionKey}`);
            const sessionSnapshot = await get(sessionRef);

            const sessionData = sessionSnapshot.val();
            if (!sessionData) {
                console.log('No session data for:', sessionKey);
                return null;
            }

            const workReqId = sessionData['work-request-id'];
            if (!workReqId) {
                console.log('No work request id in session');
                return null;
            }

            // Fetch work request details
            const workRequestRef = ref(db, `work-request/${workReqId}`);
            const workRequestSnapshot = await get(workRequestRef);

            const workReqDetail = workRequestSnapshot.val();
            if (!workReqDetail) {
                console.log('No work request detail found');
                return null;
            }

            // Check payment status
            if (!workReqDetail['paymentStatus']) {
                console.log('Payment not completed for:', workReqId);
                return null;
            }

            // Process date
            const date = new Date(workReqDetail['request-date']);
            date.setMinutes(date.getMinutes() + date.getTimezoneOffset());

            const dayName = date.getDate().toString();
            const month = MONTH_NAMES[date.getMonth()];

            // Process services
            const services = await processServices(workReqDetail['services']);

            // Fetch mechanic details
            const mechanicRef = ref(db, `mechanic/${workReqDetail['mechanic']}`);
            const mechanicSnapshot = await get(mechanicRef);

            const mechanicDetail = mechanicSnapshot.val();
            if (!mechanicDetail) {
                console.log('No mechanic detail found');
                return null;
            }

            const details: CompletedAppointmentDetails = {
                services: services,
                time: workReqDetail['request-time-range'] || 'Not specified',
                day: dayName,
                month: month,
                mechanic: workReqDetail['mechanic'],
                'mechanic-name': `${mechanicDetail['first-name']} ${mechanicDetail['last-name']}`,
                'mechanic-rate': mechanicDetail['mechanic-rating'] || 0,
                'mechanic-img': mechanicDetail['imageUrl'] || '',
                'mechanic-address': mechanicDetail['address2'] || 'Address not available',
                'mechanic-availability': mechanicDetail['availability'] || false,
            };

            return {
                'work-request-id': workReqId,
                'created-time': workReqDetail['created-time'],
                details: details,
            };

        } catch (error) {
            console.error('Error processing session:', sessionKey, error);
            return null;
        }
    };

    /**
     * Process services and sub-services
     */
    const processServices = async (servicesData: any): Promise<ServiceDetail[]> => {
        if (!servicesData) return [];

        const db = getDatabase();
        const serviceIds = Object.keys(servicesData);
        const servicesPromises = serviceIds.map(async (serviceId) => {
            try {
                // Fetch service name
                const serviceRef = ref(db, `service/${serviceId}`);
                const serviceSnapshot = await get(serviceRef);

                const serviceData = serviceSnapshot.val();
                const serviceName = serviceData ? serviceData['name'] : 'Unknown Service';

                // Process sub-services
                const subServices = servicesData[serviceId]['sub-services'] || [];
                const subServiceNames = await processSubServices(subServices);

                return {
                    service: serviceName,
                    image: 'assets/imgs/oil-icon.jpg', // Default image
                    subservice: subServiceNames,
                };
            } catch (error) {
                console.error('Error processing service:', serviceId, error);
                return {
                    service: 'Unknown Service',
                    image: 'assets/imgs/oil-icon.jpg',
                    subservice: [],
                };
            }
        });

        return Promise.all(servicesPromises);
    };

    /**
     * Process sub-services
     */
    const processSubServices = async (subServiceIds: string[]): Promise<string[]> => {
        if (!subServiceIds || subServiceIds.length === 0) return [];

        const db = getDatabase();
        const subServicePromises = subServiceIds.map(async (subServiceId) => {
            try {
                const subServiceRef = ref(db, `sub-service/${subServiceId}`);
                const snapshot = await get(subServiceRef);

                const subServiceData = snapshot.val();
                return subServiceData ? subServiceData['name'] : 'Unknown Sub-Service';
            } catch (error) {
                console.error('Error fetching sub-service:', subServiceId, error);
                return 'Unknown Sub-Service';
            }
        });

        return Promise.all(subServicePromises);
    };

    /**
     * Navigate to completed appointment details
     */
    const goToCompletedAppointment = (completedAppointment: CompletedAppointment) => {
        console.log('Navigate to completed appointment:', completedAppointment);
        const params = { 'appointment-detail': completedAppointment };
        (navigation as any).navigate('CompletedSession', params);
    };

    /**
     * Render empty state
     */
    const renderEmptyState = () => (
        <View style={styles.emptyStateContainer}>
            <View style={styles.iconContainer}>
                <Icon name="search-off" size={60} color="#c41e3a" />
            </View>
            <Text style={styles.emptyStateTitle}>SORRY</Text>
            <Text style={styles.emptyStateSubtitle}>NO DATA FOUND</Text>
        </View>
    );

    /**
     * Render list item
     */
    const renderItem = ({ item, index }: { item: CompletedAppointment; index: number }) => (
        <TouchableOpacity 
            onPress={() => goToCompletedAppointment(item)}
            activeOpacity={0.7}
        >
            <View style={styles.listItem}>
                <ProfileCard
                    imageUrl={item.details['mechanic-img']}
                    name={item.details['mechanic-name']}
                    address={item.details['mechanic-address']}
                    rating={item.details['mechanic-rate']}
                />
                <ServicesMessage
                    day={item.details.day}
                    month={item.details.month}
                    services={item.details.services}
                    time={item.details.time}
                />
            </View>
            {index !== completedAppointmentArray.length - 1 && (
                <View style={styles.separator} />
            )}
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[
                styles.container,
                { paddingTop: 0, paddingBottom: insets.bottom },
            ]}
        >
            <AppBackground />
            
            {/* Header */}
            <View style={styles.navTitle}>
                <Text style={styles.navTitleText}>SERVICE HISTORY</Text>
            </View>

            {/* Content */}
            <View style={styles.scrollArea}>
                {!loaderHistoryService ? (
                    <View style={styles.loadingContainer}>
                        <ActivityIndicator size="large" color="#fff" />
                        <Text style={styles.loadingText}>Loading service history...</Text>
                    </View>
                ) : completedAppointmentArray.length === 0 ? (
                    renderEmptyState()
                ) : (
                    <FlatList
                        data={completedAppointmentArray}
                        keyExtractor={(item) => item['work-request-id']}
                        renderItem={renderItem}
                        showsVerticalScrollIndicator={false}
                        contentContainerStyle={styles.listContentContainer}
                    />
                )}
            </View>
        </SafeAreaView>
    );
};

export default ServiceHistoryScreen;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#0c1a24',
    },
    navTitle: {
        backgroundColor: '#a00',
        paddingVertical: 12,
        alignItems: 'center',
    },
    navTitleText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 18,
        letterSpacing: 0.5,
    },
    scrollArea: {
        flex: 1,
        padding: 15,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 50,
    },
    loadingText: {
        color: '#fff',
        marginTop: 15,
        fontSize: 16,
    },
    emptyStateContainer: {
        backgroundColor: '#fff',
        padding: 40,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 16,
        marginTop: 50,
    },
    iconContainer: {
        marginBottom: 16,
    },
    emptyStateTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: '#c41e3a',
        marginBottom: 8,
    },
    emptyStateSubtitle: {
        fontSize: 14,
        color: '#666',
    },
    listContentContainer: {
        paddingBottom: 20,
    },
    listItem: {
        backgroundColor: '#fff',
        borderRadius: 8,
        marginVertical: 8,
        padding: 12,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
    },
    profileCard: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    profileImage: {
        width: 60,
        height: 60,
        borderRadius: 30,
        marginRight: 12,
        backgroundColor: '#f0f0f0',
    },
    profileName: {
        color: '#000',
        fontSize: 16,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    profileAddress: {
        color: '#666',
        fontSize: 14,
        marginBottom: 4,
    },
    ratingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    ratingText: {
        fontSize: 14,
        color: '#e7b7b7ff',
    },
    serviceMessage: {
        flexDirection: 'row',
        alignItems: 'flex-start',
    },
    serviceDateContainer: {
        backgroundColor: '#b22222',
        borderRadius: 8,
        padding: 10,
        marginRight: 12,
        alignItems: 'center',
        justifyContent: 'center',
        minWidth: 60,
    },
    serviceDateDay: {
        color: '#fff',
        fontSize: 24,
        fontWeight: 'bold',
    },
    serviceDateMonth: {
        color: '#fff',
        fontSize: 12,
        fontWeight: 'bold',
        marginTop: 2,
    },
    serviceDetailsContainer: {
        flex: 1,
        justifyContent: 'center',
    },
    serviceTitle: {
        color: '#000',
        fontSize: 15,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    serviceTime: {
        color: '#666',
        fontSize: 13,
    },
    separator: {
        height: 15,
    },
});