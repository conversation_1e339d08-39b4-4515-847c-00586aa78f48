import React, {useState, useEffect, useRef, useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  Platform,
  ActivityIndicator,
} from 'react-native';
import {request, check, PERMISSIONS, RESULTS} from 'react-native-permissions';
import {useFocusEffect} from '@react-navigation/native';
import AppBackground from '../../../components/ui/AppBackground';
import DateTimePicker from '@react-native-community/datetimepicker';
import {useNavigation} from '@react-navigation/native';
import {useForm, useFieldArray, Controller} from 'react-hook-form';
import {yupResolver} from '@hookform/resolvers/yup';
import * as yup from 'yup';
import {launchImageLibrary} from 'react-native-image-picker';
import Geolocation from 'react-native-geolocation-service';
import {GooglePlacesAutocomplete} from 'react-native-google-places-autocomplete';
import {Colors} from '../../../utils/constants/Theme';
import {
  AppCommonIcons,
  AppStrings,
  RouteNames,
} from '../../../utils/constants/AppStrings';
import CommonDropdown from '../../../components/common/CommonDropdown';
import {VehicleService} from '../../../utils/services/VehicleService';
import {useAuth} from '../../../utils/configs/AuthContext';
import axios from 'axios';
import {GooglePlacesAutocompleteDefaultProps} from '../../../utils/configs/GooglePlacesAutocompleteProps';
import Config from 'react-native-config';
import {get, push} from '@react-native-firebase/database';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import {AppointmentService} from '../../../utils/services/AppointmentService';
import {GC_AVAILABILITY, NOTIFICATION_TYPE} from '../../../utils/globals';
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';

const createValidationSchema = (screeningQuestions: any) => {
  const screeningValidation: any = {};
  Object.keys(screeningQuestions).forEach(key => {
    const question = screeningQuestions[key];
    if (question && !question.hide) {
      if (key === 'que-2') {
        screeningValidation[key] = yup
          .string()
          .when('que-1', ([que1], schema) => {
            return que1?.toLowerCase() === 'yes'
              ? schema.required('Choose atleast one')
              : schema.nullable();
          });
      } else if (key === 'que-6') {
        screeningValidation[key] = yup
          .string()
          .when('que-5', ([que5], schema) => {
            return que5?.toLowerCase() === 'yes'
              ? schema.required('Choose atleast one')
              : schema.nullable();
          });
      } else {
        if (question.conditional) {
          const conditionKey = question.conditional.dependsOn;
          const conditionValue = question.conditional.value;

          screeningValidation[key] = yup
            .string()
            .when(conditionKey, ([dependentValue], schema) => {
              return dependentValue === conditionValue
                ? schema.required(question.error || 'Choose atleast one')
                : schema.nullable();
            });
        } else {
          // FIX: Use the error message from the question data
          const errorMessage = question.error || 
            (question.type === 'entry' ? 'Type here' : 'Choose atleast one');
          screeningValidation[key] = yup
            .string()
            .required(errorMessage);
        }
      }
    }
  });

  return yup.object().shape({
    yourvehicle: yup.string().required('Please select your vehicle'),
    requestlocation: yup.string().required('Please select a location type'),
    locationInput: yup
      .mixed()
      .when('requestlocation', ([requestlocation], schema) => {
        return requestlocation === 'searchlocation'
          ? schema.required('Please select a location')
          : schema;
      }),
    serviceNeed: yup
      .array()
      .of(
        yup.object().shape({
          vehicleservice: yup.string().required('Please select a service'),
          subServices: yup
            .array()
            .of(
              yup.object().shape({
                id: yup.string().required('Sub-service ID is required'),
                name: yup.string().required('Sub-service name is required'),
              }),
            )
            .min(1, 'At least one sub-service is required'),
        }),
      )
      .min(1, 'At least one service is required'),
    alertconfirmation: yup
      .string()
      .required('Please select alert confirmation'),
   scheduledate: yup
    .mixed()
    .required('Choose date for service')
    .test('is-date', 'Choose date for service', (value) => {
      if (!value) return false;
      if (value instanceof Date) return !isNaN(value.getTime());
      if (typeof value === 'string') return !isNaN(Date.parse(value));
      return false;
    }),
    notes: yup.string(),
    ...screeningValidation,
  });
};

const Schedule: React.FC = () => {
  const navigation = useNavigation();
  const {user} = useAuth();
  const googlePlacesRef = useRef<any>(null);
  const scrollViewRef = useRef<ScrollView>(null);

  const [validationSchema, setValidationSchema] = useState(() =>
    createValidationSchema({}),
  );

  const {
    control,
    watch,
    setValue,
    formState: {errors},
    trigger,
    clearErrors,
    register,
    reset,
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      yourvehicle: '',
      requestlocation: 'currentlocation',
      locationInput: {},
      serviceNeed: [{vehicleservice: '', subServices: []}],
      alertconfirmation: '',
      scheduledate: null,
      notes: '',
    },
    mode: 'onChange',
    reValidateMode: 'onChange',
  });

  const handleAddPaymentMethod = useCallback(() => {
     navigation.navigate(RouteNames.MCX_NAV_EDIT_PAYMENT as never);
  }, [navigation]);

  useFocusEffect(
    React.useCallback(() => {
      // Network check - similar to Ionic's ionViewCanEnter
      const checkNetworkAndLoad = async () => {
        const state = await NetInfo.fetch();
        if (!state.isConnected) {
          console.log("Network check failed: ", state.isConnected);
          navigation.navigate('NetworkFailedPage' as never);
          return;
        }

        // Reset all screening answers first
        setScreeningAnswers({});

        // Clear all form errors explicitly
        clearErrors();

        // Reset all form state
        const resetData: any = {
          yourvehicle: '',
          requestlocation: 'currentlocation',
          locationInput: {},
          serviceNeed: [{vehicleservice: '', subServices: []}],
          alertconfirmation: '',
          scheduledate: null,
          notes: '',
        };

        reset(resetData);

        // Reset all other state
        setSelectedImages([]);
        setBidRates({});
        setBidErrors({});
        setSelectedSavedLocation('');
        setDate('');
        setNotes('');
        setShowDatePicker(false);
        setDatePickerValue(new Date());
        setLocationSaved(false);

        // Scroll to top when screen gains focus
        scrollViewRef.current?.scrollTo({ y: 0, animated: true });

        const loadCardDetailsAndCheck = async () => {
          try {
            const hasCard = await VehicleService.fetchCustomerPaymentMethod(
              user?.uid || '',
            );
            setCustomerCard(hasCard ? {} : null);
            if (!hasCard) {
              Alert.alert(
                'No payment method',
                'You are not eligible to schedule services without a payment method. Please add one.',
                [
                  {
                    text: 'Cancel',
                    onPress: () =>
                      navigation.navigate(RouteNames.MCX_DASHBOARD as never),
                  },
                  {text: 'OK', onPress: () => handleAddPaymentMethod()},
                ],
              );
            }
          } catch (error) {
            setCustomerCard(null);
          }
        };
        if (user?.uid) {
          loadCardDetailsAndCheck();
        }
      };

      checkNetworkAndLoad();
    }, [handleAddPaymentMethod, reset, clearErrors, user?.uid, navigation]),
  );

  const {
    fields: serviceFields,
    append: appendService,
    remove: removeService,
  } = useFieldArray({
    control,
    name: 'serviceNeed',
  });

  const addSubService = (serviceIndex: number, subService: any) => {
    const currentServices = watch('serviceNeed');
    const updatedServices = [...currentServices];
    const currentSubServices = updatedServices[serviceIndex].subServices || [];
    if (!currentSubServices.find((s: any) => s.id === subService.id)) {
      updatedServices[serviceIndex].subServices = [
        ...currentSubServices,
        subService,
      ];
      setValue(
        `serviceNeed.${serviceIndex}.subServices`,
        updatedServices[serviceIndex].subServices,
      );
    }
  };

  const removeSubService = (serviceIndex: number, subServiceId: string) => {
    const currentServices = watch('serviceNeed');
    const updatedServices = [...currentServices];
    updatedServices[serviceIndex].subServices = updatedServices[
      serviceIndex
    ].subServices.filter((s: any) => s.id !== subServiceId);
    setValue(
      `serviceNeed.${serviceIndex}.subServices`,
      updatedServices[serviceIndex].subServices,
    );
  };

  const [userVehicleList, setUserVehicleList] = useState<any[]>([]);
  const [isLoadingVehicles, setIsLoadingVehicles] = useState(true);
  const [customerRequestAdrress, setCustomerRequestAdrress] =
    useState<any>(null);
  const [_selectedLocation, _setSelectedLocation] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);
  const [selectedImages, setSelectedImages] = useState<any[]>([]);

  const handleDeleteImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, idx) => idx !== index));
  };
  const [savedLocations, setSavedLocations] = useState<any[]>([]);
  const [isLoadingLocations, setIsLoadingLocations] = useState(true);
  const [_isLoading, _setIsLoading] = useState(false);
  const [screeningAnswers, setScreeningAnswers] = useState<
    Record<string, string | null>
  >({});
  const [date, setDate] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedSavedLocation, setSelectedSavedLocation] = useState('');
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [datePickerValue, setDatePickerValue] = useState<Date>(new Date());
  const [screeningQuestions, setScreeningQuestions] = useState<any>({});
  const [alertConfirmationData, setAlertConfirmationData] = useState<any[]>([]);
  const [isLoadingAlerts, setIsLoadingAlerts] = useState(true);
  const [bidErrors, setBidErrors] = useState<Record<string, string>>({});
  const [serviceIdNameMap, setServiceIdNameMap] = useState<
    Record<string, string>
  >({});
  const [isLoadingServices, setIsLoadingServices] = useState(true);
  const [subServiceIdNameMap, setSubServiceIdNameMap] = useState<
    Record<string, string>
  >({});
  const [subServicePriceData, setSubServicePriceData] = useState<
    Record<string, any>
  >({});
  const [isLoadingSubServices, setIsLoadingSubServices] = useState(true);
  const [bidRates, setBidRates] = useState<Record<string, string>>({});
  const [bidValidationTimeouts, setBidValidationTimeouts] = useState<
    Record<string, NodeJS.Timeout>
  >({});
  const [mechanicList, setMechanicList] = useState<any>({});
  const [customerCard, setCustomerCard] = useState<any>(null);
  const [locationSaved, setLocationSaved] = useState(false);

  useEffect(() => {
    const que1 = String(screeningAnswers['que-1'] || '').toLowerCase();
    if (que1 !== 'yes' && screeningAnswers['que-2']) {
      setScreeningAnswers(prev => ({...prev, 'que-2': null}));
      setValue('que-2', null);
      clearErrors('que-2');
      trigger('que-2');
    }

    const que5 = String(screeningAnswers['que-5'] || '').toLowerCase();
    if (que5 !== 'yes' && screeningAnswers['que-6']) {
      setScreeningAnswers(prev => ({...prev, 'que-6': null}));
      setValue('que-6', null);
      clearErrors('que-6');
      trigger('que-6');
    }
  }, [screeningAnswers, clearErrors, setValue, trigger]);

  const fetchSavedLocations = useCallback(async () => {
    try {
      if (user?.uid) {
        const locations = await VehicleService.fetchSavedLocations(user.uid);
        setSavedLocations(locations);
      }
    } catch (error) {
      console.error('Error fetching saved locations:', error);
      setSavedLocations([]);
    } finally {
      setIsLoadingLocations(false);
    }
  }, [user?.uid]);

  useEffect(() => {
    const fetchScreeningQuestions = async () => {
      try {
        const snapshot = await get(VehicleService.getPreScreeningQuestions());
        const screeningData = snapshot.val();
        if (screeningData) {
          setScreeningQuestions(screeningData);
          const newValidationSchema = createValidationSchema(screeningData);
          setValidationSchema(newValidationSchema);
          const initialAnswers: Record<string, string | null> = {};
          Object.keys(screeningData).forEach(key => {
            if (screeningData[key] && !screeningData[key].hide) {
              initialAnswers[key] = null;
            }
          });
          setScreeningAnswers(initialAnswers);
        } else {
          setScreeningQuestions({});
          setValidationSchema(createValidationSchema({}));
        }
      } catch (error) {
        console.error('Error fetching screening questions:', error);
        setScreeningQuestions({});
        setValidationSchema(createValidationSchema({}));
      }
    };
    const fetchAlertConfirmations = async () => {
      try {
        const data = await VehicleService.getAlertConfirmation();
        setAlertConfirmationData(data);
      } catch (error) {
        console.error('Error fetching alert confirmations:', error);
      } finally {
        setIsLoadingAlerts(false);
      }
    };
    const fetchServiceMaps = async () => {
      try {
        const serviceMap = await VehicleService.getServiceIdNameMap();
        const subServiceMap = await VehicleService.getSubServiceIdNameMap();
        setServiceIdNameMap(serviceMap);
        setSubServiceIdNameMap(subServiceMap);
      } catch (error) {
        console.error('Error fetching service maps:', error);
      } finally {
        setIsLoadingServices(false);
      }
    };
    fetchScreeningQuestions();
    fetchAlertConfirmations();
    fetchServiceMaps();
    const fetchSubServicePriceData = async () => {
      try {
        const priceData = await VehicleService.getSubServicePriceData();
        setSubServicePriceData(priceData);
      } catch (error) {
        console.error('Error fetching sub-service price data:', error);
      } finally {
        setIsLoadingSubServices(false);
      }
    };
    fetchSubServicePriceData();
    if (user?.uid) {
      fetchUserVehicles(user.uid);
    }
    fetchSavedLocations();
  }, [user, fetchSavedLocations]);

  useEffect(() => {
    Object.keys(screeningQuestions).forEach(key => {
      const q = screeningQuestions[key];
      if (q && !q.hide) {
        try {
          register(key);
        } catch (e) {}
        if (screeningAnswers[key] === undefined) {
          setValue(key, null);
          // Clear any existing errors for this field
          clearErrors(key);
        }
      }
    });
  }, [screeningQuestions, register, setValue, screeningAnswers, clearErrors]);

  useEffect(() => {
    const que1 = String(screeningAnswers['que-1'] || '').toLowerCase();
    if (que1 !== 'yes' && screeningAnswers['que-2']) {
      setScreeningAnswers(prev => ({...prev, 'que-2': null}));
      setValue('que-2', null);
      clearErrors('que-2');
    }

    const que5 = String(screeningAnswers['que-5'] || '').toLowerCase();
    if (que5 !== 'yes' && screeningAnswers['que-6']) {
      setScreeningAnswers(prev => ({...prev, 'que-6': null}));
      setValue('que-6', null);
      clearErrors('que-6');
    }
  }, [screeningAnswers, clearErrors, setValue]);

  useEffect(() => {
    if (selectedSavedLocation) {
      const location = savedLocations.find(l => l.id === selectedSavedLocation);
      if (location) {
        const addressParts = location.address
          .split(',')
          .map((part: string) => part.trim());
        setCustomerRequestAdrress({
          address_array: {
            address1: addressParts[0] || '',
            address2: '',
            city: addressParts[1] || '',
            state: addressParts[2] || '',
            country: addressParts[3] || 'India',
            zipcode: '',
          },
          formatted_address: location.address,
          location: location.coordinates,
        });
        _setSelectedLocation(location.coordinates);
      }
    }
  }, [selectedSavedLocation, savedLocations]);

  useEffect(() => {
    const subscription = watch((value, {name, type}) => {
      if (name && name.startsWith('que-') && type === 'change') {
        const timeoutId = setTimeout(() => {
          trigger(name);
        }, 50);
        return () => clearTimeout(timeoutId);
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, trigger]);

  const watchedServiceNeed = watch('serviceNeed');

  const fetchAllMechanic = useCallback(async () => {
    try {
      const latitude = customerRequestAdrress?.location?.latitude || 0;
      const longitude = customerRequestAdrress?.location?.longitude || 0;
      const serviceIds = watchedServiceNeed
        .map((s: any) => s.vehicleservice)
        .filter(Boolean);
      const mechanics = await VehicleService.fetchMechanicAvailability(
        latitude,
        longitude,
        serviceIds,
      );
      setMechanicList(mechanics);
    } catch (error) {
      console.error('Error fetching mechanic availability:', error);
      setMechanicList({});
    }
  }, [customerRequestAdrress, watchedServiceNeed]);

  useEffect(() => {
    if (customerRequestAdrress && watchedServiceNeed.length > 0) {
      fetchAllMechanic();
    }
  }, [customerRequestAdrress, watchedServiceNeed, fetchAllMechanic]);

  const fetchUserVehicles = async (userId: string) => {
    try {
      if (userId) {
        const vehicles = await VehicleService.getVehicles(userId);
        setUserVehicleList(vehicles);
      }
    } catch (error) {
      console.error('Error fetching user vehicles:', error);
    } finally {
      setIsLoadingVehicles(false);
    }
  };

const handleConfirmAppointment = async (data: any) => {
  _setIsLoading(true);
  try {
    const requestDate = data.scheduledate.toISOString().split('T')[0];
    const selectedAlert = alertConfirmationData.find(
      alert => alert.value === data.alertconfirmation,
    );
    
    let alertValue = '30';
    if (selectedAlert?.label) {
      const match = selectedAlert.label.match(/(\d+(?:\.\d+)?)\s*(hour|minute)/i);
      if (match) {
        const value = parseFloat(match[1]);
        const unit = match[2].toLowerCase();
        if (unit.includes('hour')) {
          alertValue = (value * 60).toString();
        } else {
          alertValue = value.toString();
        }
      }
    }

    const screeningFAQ: Record<string, any> = {};
    Object.keys(screeningAnswers).forEach(key => {
      if (screeningAnswers[key] && screeningQuestions[key]) {
        screeningFAQ[key] = {
          answer: screeningAnswers[key].toUpperCase(),
          questions: screeningQuestions[key].question,
        };
      }
    });

    const services: Record<string, any> = {};
    data.serviceNeed.forEach((service: any, index: number) => {
      if (service.vehicleservice && service.subServices && service.subServices.length > 0) {
        const serviceKey = service.vehicleservice;
        const customerBids: string[] = [];
        const subServiceIds: string[] = [];

        service.subServices.forEach((subService: any, subIndex: number) => {
          const bidKey = `${index}-${subIndex}`;
          const bidRate = bidRates[bidKey] || '0';
          customerBids.push(bidRate);
          subServiceIds.push(subService.id);
        });

        services[serviceKey] = {
          'customer-bid': customerBids,
          'mechanic-bid': customerBids.map(() => '0'),
          'service-type': serviceKey,
          'sub-services': subServiceIds,
        };
      }
    });

    const workRequestId = push(VehicleService.createMultipleWorkRequest()).key || '';
    console.log('Created work request ID:', workRequestId);

    const availability = (await AsyncStorage.getItem(GC_AVAILABILITY)) || 'Appointment';

    // Create the work request first WITHOUT images
    await VehicleService.updateMultipleWorkRequest(
      mechanicList,
      workRequestId,
      notes || '',
      '', // Empty image URL - will be updated after upload
      alertValue,
      availability,
      requestDate,
      '1:00 pm - 4:30 pm',
      services,
      data.yourvehicle || '',
      {
        address_array: {
          address1: customerRequestAdrress?.address_array?.address1 || '',
          address2: customerRequestAdrress?.address_array?.address2 || '',
          city: customerRequestAdrress?.address_array?.city || '',
          country: customerRequestAdrress?.address_array?.country || 'India',
          state: customerRequestAdrress?.address_array?.state || '',
          zipcode: customerRequestAdrress?.address_array?.zipcode || '',
        },
        formatted_address: customerRequestAdrress?.formatted_address || '',
        location: {
          latitude: customerRequestAdrress?.location?.latitude || 0,
          longitude: customerRequestAdrress?.location?.longitude || 0,
        },
      },
      screeningFAQ,
      user?.uid || '',
    );

    // Upload images AFTER work request creation (matching Ionic flow)
    if (selectedImages.length > 0) {
      console.log('Uploading images...');
      const imageUris = selectedImages.map(img => img.uri);
      await uploadImagesToWorkRequest(workRequestId, imageUris);
      console.log('Images uploaded successfully');
    }

    // Update work request status
    await VehicleService.updateWorkRequestStatus(workRequestId, 'pending');

    // Send FCM notification
    try {
      const userProfile = VehicleService.fetchUserInformation(user?.uid || '');
      const userSnapshot = await get(userProfile);
      const userInfo = userSnapshot.val();
      const customerName = userInfo
        ? `${userInfo['first-name'] || ''} ${userInfo['last-name'] || ''}`.trim()
        : 'Customer';
      
      await AppointmentService.sendPUSHToUser(
        workRequestId,
        'Work request received',
        `${customerName} has sent a work request`,
         'workrequest',
          workRequestId,
      );
      console.log('FCM notification sent');
    } catch (notificationError) {
      console.error('❌ Error sending FCM notification:', notificationError);
    }

    Alert.alert(
      'Success',
      'Appointment confirmed and work request created!',
      [
        {
          text: 'OK',
          onPress: () => navigation.navigate(RouteNames.MCX_DASHBOARD as never),
        },
      ],
    );
  } catch (error) {
    console.error('Error submitting appointment:', error);
    Alert.alert('Error', 'Failed to submit appointment. Please try again.');
  } finally {
    _setIsLoading(false);
  }
};

const uploadImagesToWorkRequest = async (workRequestId: string, imageUris: string[]) => {
  try { 
    const uploadPromises = imageUris.map(async (uri, index) => {
      try {        
        const snapshot = await VehicleService.uploadImage(
          uri, 
          workRequestId, 
          index, 
          'appointmentImages'
        );
        
        // Get the full path from snapshot
        const profileURL = snapshot.metadata.fullPath;
        
        // Get download URL
        const downloadURL = await VehicleService.getDownloadURL(profileURL);
        
        return downloadURL;
      } catch (error) {
        console.error(`Error uploading image ${index}:`, error);
        throw error;
      }
    });

    // Wait for all uploads to complete
    const uploadedUrls = await Promise.all(uploadPromises);
    
    // Update the multiple-request-data with image URLs (as array)
    await VehicleService.updateMultipleRequestImagePath(uploadedUrls, workRequestId);
    
    return uploadedUrls;
  } catch (error) {
    console.error('Error in uploadImagesToWorkRequest:', error);
    throw error;
  }
};
  const validateBidRates = (): {hasError: boolean; errorMessage: string} => {
    const serviceNeed = watch('serviceNeed');
    let hasError = false;
    let errorMessage = '';

    serviceNeed.forEach((service: any, index: number) => {
      if (
        service.vehicleservice &&
        service.subServices &&
        service.subServices.length > 0
      ) {
        service.subServices.forEach((subService: any, subIndex: number) => {
          const bidKey = `${index}-${subIndex}`;
          const bidRateStr = bidRates[bidKey] || '';
          const bidRate = parseFloat(bidRateStr);
          const subServiceData = subServicePriceData[subService.id] || {};
          const minPrice = parseFloat(
            subServiceData['min-price'] || subServiceData.minPrice || '0',
          );
          const maxPrice = parseFloat(
            subServiceData['max-price'] || subServiceData.maxPrice || '0',
          );

          if (!bidRateStr || bidRateStr.trim() === '') {
            hasError = true;
            errorMessage = 'Please add your bid on price model list';
            return;
          }

          if (isNaN(bidRate) || bidRate <= 0) {
            hasError = true;
            errorMessage = 'Please enter a valid bid amount';
            return;
          }

          if (
            minPrice &&
            maxPrice &&
            (bidRate < minPrice || bidRate > maxPrice)
          ) {
            hasError = true;
            errorMessage = `Please enter an amount between $${minPrice} - ${maxPrice}`;
            return;
          }
        });
      }
    });

    return {hasError, errorMessage};
  };
  const handleSubmitWithValidation = async () => {
    const currentValues = watch();

    // Clear previous bid errors
    setBidErrors({});

    let locationError = false;
    if (
      currentValues.requestlocation === 'currentlocation' &&
      !customerRequestAdrress
    ) {
      locationError = true;
      Alert.alert(
        'Location Required',
        'Please wait for current location to load or select a different location type.',
      );
      return;
    } else if (
      currentValues.requestlocation === 'savedlocation' &&
      !selectedSavedLocation
    ) {
      locationError = true;
      Alert.alert('Location Required', 'Please select a saved location.');
      return;
    } else if (
      currentValues.requestlocation === 'searchlocation' &&
      !customerRequestAdrress
    ) {
      locationError = true;
      Alert.alert('Location Required', 'Please search and select a location.');
      return;
    }

    const isValid = await trigger();

    if (!isValid || locationError) {
      if (!isValid) {
        Alert.alert(
          'Please fill all the entries',
          'Some fields are missing or invalid. Please complete all required fields.',
        );
      }
      return;
    }

    const screeningErrorKeys = Object.keys(errors).filter(
      key => key.startsWith('que-') && errors[key],
    );

    if (screeningErrorKeys.length > 0) {
      Alert.alert(
        'Incomplete Screening',
        'Please complete all required screening questions before submitting.',
      );
      return;
    }

    // Validate bid rates with detailed error checking
    for (let index = 0; index < currentValues.serviceNeed.length; index++) {
      const service = currentValues.serviceNeed[index];

      if (!service.vehicleservice) {
        Alert.alert(
          'Check Bid List',
          'Service type is missing for service #' + (index + 1),
        );
        return;
      }

      if (!service.subServices || service.subServices.length === 0) {
        Alert.alert(
          'Check Bid List',
          'No sub-services selected for service #' + (index + 1),
        );
        return;
      }

      // Validate each sub-service in this service
      for (
        let subIndex = 0;
        subIndex < service.subServices.length;
        subIndex++
      ) {
        const subService = service.subServices[subIndex];
        const bidKey = `${index}-${subIndex}`;
        const bidRateStr = bidRates[bidKey] || '';
        const bidRate = parseFloat(bidRateStr);

        const subServiceData = subServicePriceData[subService.id];
        if (!subServiceData) {
          Alert.alert(
            'Check Bid List',
            `Price data not found for sub-service "${subService.name}"`,
          );
          return;
        }

        const minPrice =
          subServiceData['min-price'] ||
          subServiceData.minPrice ||
          subServiceData.min_rate ||
          0;
        const maxPrice =
          subServiceData['max-price'] ||
          subServiceData.maxPrice ||
          subServiceData.max_rate ||
          Number.MAX_SAFE_INTEGER;

        const priceType =
          subServiceData['price-type'] ||
          subServiceData.price_type ||
          subServiceData.priceType;

        // Skip validation for flat price types OR if bid is already set
        if (priceType === 'flat') {
          // Ensure flat price is set if not already
          if (!bidRateStr || bidRateStr.trim() === '') {
            const flatPrice =
              minPrice || subServiceData.price || subServiceData.rate || 0;
            setBidRates(prev => ({...prev, [bidKey]: flatPrice.toString()}));
          }
          continue; // Skip other validations for flat prices
        }

        if (!bidRateStr || bidRateStr.trim() === '') {
          Alert.alert(
            'Check Bid List',
            `Please add your bid for "${subService.name}"`,
          );
          return;
        }

        if (isNaN(bidRate) || bidRate <= 0) {
          Alert.alert(
            'Check Bid List',
            `Please enter a valid bid amount for "${subService.name}"`,
          );
          return;
        }

        if (bidRate < minPrice || bidRate > maxPrice) {
          Alert.alert(
            'Check Bid List',
            `Please enter an amount between $${minPrice} - $${maxPrice} for "${subService.name}"`,
          );
          return;
        }
      }
    }

    if (!customerCard) {
      Alert.alert(
        'No payment method',
        'You are not eligible to pay services, please add a payment method',
        [
          {
            text: 'Cancel',
            onPress: () => navigation.navigate('DashBoard' as never),
          },
          {text: 'OK', onPress: () => handleAddPaymentMethod()},
        ],
      );
      return;
    }

    if (Object.keys(mechanicList).length === 0) {
      Alert.alert(
        "Sorry, Service can't be scheduled.",
        'No mechanic available around the area you have chosen.',
      );
      return;
    }

    await handleConfirmAppointment(currentValues);
  };

  const handleCancel = () => {
    Alert.alert(
      'Confirm',
      'Are you sure you want to leave this screen? All entered data will be lost.',
      [
        {text: 'Cancel', style: 'cancel'},
        {text: 'OK', onPress: () => navigation.goBack()},
      ],
    );
  };

  const handleLocationTypeChange = (type: string) => {
    setValue('requestlocation', type);
    setLocationSaved(false); // Reset location saved state when changing location type
    if (type === 'currentlocation') {
      requestCurrentLocation();
    } else if (type === 'savedlocation') {
      requestSavedLocation();
    } else {
      setCustomerRequestAdrress(null);
      _setSelectedLocation(null);
      if (googlePlacesRef.current) {
        googlePlacesRef.current.setAddressText('');
      }
    }
  };

  const processLocation = useCallback(
    async (latitude: number, longitude: number) => {
      try {
        const response = await axios.get(
          `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_PLACES_API_KEY}`,
        );

        if (response.data.results.length > 0) {
          const result = response.data.results[0];
          const parsedAddress = parseAddressComponents(
            result.address_components || [],
            result.formatted_address || '',
          );
          const locationData = {
            address_array: parsedAddress,
            formatted_address: result.formatted_address,
            location: {latitude, longitude},
          };
          setCustomerRequestAdrress(locationData);
          setValue('locationInput', {
            formatted_address: result.formatted_address,
            geometry: {location: {lat: latitude, lng: longitude}},
            address_components: result.address_components || [],
          });
          _setSelectedLocation({latitude, longitude});
        } else {
          console.log('Schedule: No geocoding results found');
        }
      } catch (error) {
        console.error('Schedule: Error in processLocation:', error);
      }
    },
    [setValue],
  );

  const requestCurrentLocation = useCallback(async () => {
    try {
      const permission =
        Platform.OS === 'ios'
          ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
          : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
      let status = await check(permission);
      if (status !== RESULTS.GRANTED) {
        console.log('Schedule: Requesting permission');
        status = await request(permission);
        console.log('Schedule: Permission after request:', status);
      }
      if (status === RESULTS.GRANTED) {
        console.log('Schedule: Permission granted, getting position');
        Geolocation.getCurrentPosition(
          async position => {
            console.log('Schedule: Got position:', position.coords);
            const {latitude, longitude} = position.coords;
            if (user?.uid) {
              try {
                await VehicleService.updateCustomerLocation(user.uid, {
                  latitude,
                  longitude,
                });
                console.log('Schedule: Updated location in DB');
              } catch (error) {
                console.error('Error updating location in DB:', error);
              }
            }
            await processLocation(latitude, longitude);
          },
          error => {
            console.error('Schedule: Geolocation error:', error);
            Alert.alert('Error', 'Unable to get current location');
          },
          {enableHighAccuracy: true, timeout: 15000, maximumAge: 10000},
        );
      } else {
        console.log('Schedule: Permission denied');
        Alert.alert('Permission Denied', 'Location permission is required');
      }
    } catch (error) {
      console.error('Schedule: Location permission error:', error);
    }
  }, [user?.uid, processLocation]);

  useEffect(() => {
    const loadCurrentLocation = async () => {
      // Only load if current location is selected and not already loaded
      const currentLocationType = watch('requestlocation');
      if (
        currentLocationType === 'currentlocation' &&
        !customerRequestAdrress
      ) {
        try {
          await requestCurrentLocation();
        } catch (error) {
          console.error('Error loading current location:', error);
        }
      }
    };
    loadCurrentLocation();
  }, [requestCurrentLocation, watch, customerRequestAdrress]);

  // useEffect(() => {
  //   const today = new Date();
  //   setDatePickerValue(today);
  //   const month = (today.getMonth() + 1).toString().padStart(2, '0');
  //   const day = today.getDate().toString().padStart(2, '0');
  //   const year = today.getFullYear().toString();
  //   const formattedDate = `${month}/${day}/${year}`;
  //   setDate(formattedDate);
  //   //setValue('scheduledate', today);
  // }, [setValue]);

  const requestSavedLocation = () => {
    // No alert needed - dropdown will show placeholder when empty
  };

  const parseAddressComponents = (
    addressComponents: any[],
    formattedAddress: string,
  ) => {
    const addressData = {
      address1: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      zipcode: '',
    };

    if (addressComponents && addressComponents.length > 0) {
      addressComponents.forEach((component: any) => {
        const types = component.types;

        if (types.includes('street_number') || types.includes('route')) {
          if (!addressData.address1) {
            addressData.address1 = component.long_name;
          } else {
            addressData.address1 += ' ' + component.long_name;
          }
        } else if (
          types.includes('sublocality') ||
          types.includes('neighborhood')
        ) {
          addressData.address2 = component.long_name;
        } else if (
          types.includes('locality') ||
          types.includes('administrative_area_level_2')
        ) {
          addressData.city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          addressData.state = component.long_name;
        } else if (types.includes('country')) {
          addressData.country = component.long_name;
        } else if (types.includes('postal_code')) {
          addressData.zipcode = component.long_name;
        }
      });
    }
    if (!addressData.address1 && formattedAddress) {
      const parts = formattedAddress.split(',');
      addressData.address1 = parts[0]?.trim() || '';
      if (parts.length > 1 && !addressData.city) {
        addressData.city = parts[1]?.trim() || '';
      }
      if (parts.length > 2 && !addressData.state) {
        addressData.state = parts[2]?.trim() || '';
      }
    }

    return addressData;
  };

  const handleDateChange = (event: any, selectedDate?: Date) => {
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (selectedDate) {
      setDatePickerValue(selectedDate);
      const month = (selectedDate.getMonth() + 1).toString().padStart(2, '0');
      const day = selectedDate.getDate().toString().padStart(2, '0');
      const year = selectedDate.getFullYear().toString();
      const formattedDate = `${month}/${day}/${year}`;
      setDate(formattedDate);
      setValue('scheduledate', selectedDate);
      trigger('scheduledate');

      if (Platform.OS === 'ios') {
        setShowDatePicker(false);
      }
    } else if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }
  };

  const _handleImagePick = () => {
    const options = {
      mediaType: 'photo' as const,
      includeBase64: false,
      maxHeight: 2000,
      maxWidth: 2000,
    };

    launchImageLibrary(options, response => {
      if (response.didCancel) {
        console.log('User cancelled image picker');
      } else if (response.errorMessage) {
        console.error('ImagePicker Error: ', response.errorMessage);
      } else if (response.assets && response.assets[0]) {
        setSelectedImages([...selectedImages, response.assets[0]]);
      }
    });
  };

  return (
    <View style={styles.container}>
      <AppBackground />
      <View style={styles.header}>
        <Text style={styles.headerTitle}>
          {AppStrings.MCX_SCHEDULE_SERVICE_TITLE}
        </Text>
      </View>

      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled">
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {AppStrings.MCX_MY_VEHICLE_TEXT}
          </Text>
          <View style={styles.dropdownContainer}>
            <Text style={styles.dropdownLabel}>
              {AppStrings.MCX_MY_VEHICLE_TEXT}
            </Text>
            <Controller
              control={control}
              name="yourvehicle"
              render={({field: {onChange, value}}) => (
                <CommonDropdown
                  data={userVehicleList.map(item => ({
                    label: String(item.label),
                    value: String(item.value),
                  }))}
                  value={value}
                  onValueChange={onChange}
                  placeholder="Select my vehicle"
                  loading={isLoadingVehicles}
                />
              )}
            />
            {errors.yourvehicle && (
              <Text style={styles.errorText}>
                {String((errors.yourvehicle as any)?.message || '')}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {AppStrings.MCX_MY_LOCATION_TEXT}
          </Text>
          <Controller
            control={control}
            name="requestlocation"
            render={({field: {onChange, value}}) => (
              <View style={styles.radioGroup}>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => {
                    onChange('currentlocation');
                    handleLocationTypeChange('currentlocation');
                  }}>
                  <Text style={styles.optionText}>
                    {AppStrings.MCX_CHOOSE_CURRENT_LOCATION_TEXT}
                  </Text>
                  <View
                    style={[
                      styles.radioCircle,
                      value === 'currentlocation' && styles.radioCircleSelected,
                    ]}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => {
                    onChange('savedlocation');
                    handleLocationTypeChange('savedlocation');
                  }}>
                  <Text style={styles.optionText}>
                    {AppStrings.MCX_SAVED_LOCATION_TEXT}
                  </Text>
                  <View
                    style={[
                      styles.radioCircle,
                      value === 'savedlocation' && styles.radioCircleSelected,
                    ]}
                  />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.radioOption}
                  onPress={() => {
                    onChange('searchlocation');
                    handleLocationTypeChange('searchlocation');
                  }}>
                  <Text style={styles.optionText}>
                    {AppStrings.MCX_SEARCH_NEARBY_LOCATION_TEXT}
                  </Text>
                  <View
                    style={[
                      styles.radioCircle,
                      value === 'searchlocation' && styles.radioCircleSelected,
                    ]}
                  />
                </TouchableOpacity>
              </View>
            )}
          />

          {watch('requestlocation') === 'searchlocation' && (
            <View style={styles.locationInputContainer}>
              <GooglePlacesAutocomplete
                {...GooglePlacesAutocompleteDefaultProps}
                ref={googlePlacesRef}
                placeholder="Search for a location"
                minLength={2}
                fetchDetails={true}
                onPress={(data, details = null) => {
                  if (details) {
                    // Reset locationSaved flag when new location selected
                    setLocationSaved(false);

                    setValue('locationInput', details);
                    const parsedAddress = parseAddressComponents(
                      details.address_components || [],
                      details.formatted_address || '',
                    );
                    setCustomerRequestAdrress({
                      address_array: parsedAddress,
                      formatted_address: details.formatted_address,
                      location: {
                        latitude: details.geometry.location.lat,
                        longitude: details.geometry.location.lng,
                      },
                    });
                    _setSelectedLocation({
                      latitude: details.geometry.location.lat,
                      longitude: details.geometry.location.lng,
                    });
                  }
                }}
                query={{
                  key: Config.GOOGLE_PLACES_API_KEY,
                  language: 'en',
                  components: 'country:in',
                }}
                debounce={300}
                styles={{
                  container: {flex: 0, zIndex: 1000},
                  textInput: [styles.locationInput, styles.locationInputText],
                  textInputContainer: styles.locationInputContainer,
                  listView: styles.locationListView,
                  row: styles.locationRow,
                  description: styles.locationDescription,
                  placeholder: styles.locationInputPlaceholder,
                }}
                enablePoweredByContainer={false}
              />
              {errors.locationInput && (
                <Text style={styles.errorText}>
                  {String(
                    (errors.locationInput as any)?.message ||
                      'Location is required',
                  )}
                </Text>
              )}
            </View>
          )}

          {(watch('requestlocation') === 'currentlocation' ||
            watch('requestlocation') === 'selectedlocation') && (
            <View style={styles.locationDisplay}>
              <Text style={styles.locationText}>
                {customerRequestAdrress
                  ? String(customerRequestAdrress.formatted_address)
                  : 'Loading location...'}
              </Text>
            </View>
          )}

          {watch('requestlocation') === 'searchlocation' &&
            customerRequestAdrress && (
              <View style={styles.locationDisplay}>
                <Text style={styles.locationText}>
                  {String(customerRequestAdrress.formatted_address)}
                </Text>
                {(() => {
                  const isAlreadySaved = savedLocations.some(savedLoc =>
                    savedLoc.address === customerRequestAdrress.formatted_address ||
                    (savedLoc.coordinates?.latitude === customerRequestAdrress.location?.latitude &&
                     savedLoc.coordinates?.longitude === customerRequestAdrress.location?.longitude)
                  );

                  return (
                    <TouchableOpacity
                      style={[
                        styles.saveLocationButton,
                        (locationSaved || isAlreadySaved) && styles.disabledButton,
                      ]}
                      disabled={locationSaved || isAlreadySaved}
                      onPress={async () => {
                        try {
                          if (user?.uid && customerRequestAdrress) {
                            const locationData = {
                              name: String(
                                customerRequestAdrress.formatted_address.split(
                                  ',',
                                )[0] || 'Searched Location',
                              ),
                              address: String(
                                customerRequestAdrress.formatted_address,
                              ),
                              coordinates: customerRequestAdrress.location,
                            };

                            await VehicleService.saveLocation(
                              user.uid,
                              locationData,
                            );
                            await fetchSavedLocations();
                            setLocationSaved(true);

                            Alert.alert('Success', 'Location saved!');
                          }
                        } catch (error) {
                          console.error('Error saving location:', error);
                          Alert.alert(
                            'Error',
                            'Failed to save location. Please try again.',
                          );
                        }
                      }}>
                      <Text
                        style={[
                          styles.saveLocationText,
                          (locationSaved || isAlreadySaved) && styles.disabledText,
                        ]}>
                        {locationSaved
                          ? 'Location Saved'
                          : isAlreadySaved
                          ? 'Location Already Saved'
                          : AppStrings.MCX_SAVE_LOCATION_TEXT}
                      </Text>
                    </TouchableOpacity>
                  );
                })()}
              </View>
            )}

          {watch('requestlocation') === 'savedlocation' && (
            <View style={styles.dropdownContainer}>
              <Text style={styles.dropdownLabel}>
                {AppStrings.MCX_SELECT_SAVED_LOCATION_LABEL}
              </Text>
              <CommonDropdown
                data={savedLocations.map(l => ({
                  label: String(l.name),
                  value: l.id,
                }))}
                value={selectedSavedLocation}
                onValueChange={setSelectedSavedLocation}
                placeholder="Select saved location"
                loading={isLoadingLocations}
              />
            </View>
          )}
        </View>

        <View style={styles.section}>
          <Text style={styles.servicesTitle}>
            {AppStrings.MCX_SERVICES_TITLE}
          </Text>
          {serviceFields.map((field, index) => (
            <View key={field.id} style={styles.serviceItem}>
              <View>
                <View style={styles.dropdownContainer}>
                  <Text style={styles.dropdownLabel}>
                    {AppStrings.MCX_SERVICE_TYPE_KEYWORD}
                  </Text>
                  <Controller
                    control={control}
                    name={`serviceNeed.${index}.vehicleservice`}
                    render={({field: {onChange, value}}) => {
                      // Get all currently selected service IDs except current field
                      const selectedServiceIds = watchedServiceNeed
                        .map((s: any, i: number) =>
                          i !== index ? s.vehicleservice : null,
                        )
                        .filter(Boolean);

                      // Filter out already selected services
                      const availableServices = Object.keys(
                        serviceIdNameMap || {},
                      )
                        .filter(key => !selectedServiceIds.includes(key))
                        .map(key => ({
                          label: (serviceIdNameMap || {})[key] || key,
                          value: key,
                        }));

                      return (
                        <CommonDropdown
                          data={availableServices}
                          value={value}
                          onValueChange={val => {
                            onChange(val);
                            setValue(`serviceNeed.${index}.subServices`, []);

                            // Clear bid rates for this service index
                            const newBidRates = {...bidRates};
                            Object.keys(newBidRates).forEach(key => {
                              if (key.startsWith(`${index}-`)) {
                                delete newBidRates[key];
                              }
                            });
                            setBidRates(newBidRates);

                            // Clear bid errors for this service index
                            const newBidErrors = {...bidErrors};
                            Object.keys(newBidErrors).forEach(key => {
                              if (key.startsWith(`${index}-`)) {
                                delete newBidErrors[key];
                              }
                            });
                            setBidErrors(newBidErrors);

                            // Clear validation timeouts for this service index
                            const newTimeouts = {...bidValidationTimeouts};
                            Object.keys(newTimeouts).forEach(key => {
                              if (key.startsWith(`${index}-`)) {
                                clearTimeout(newTimeouts[key]);
                                delete newTimeouts[key];
                              }
                            });
                            setBidValidationTimeouts(newTimeouts);
                          }}
                          placeholder="Select service"
                          loading={isLoadingServices}
                        />
                      );
                    }}
                  />
                  {errors.serviceNeed &&
                    (errors.serviceNeed as any)[index]?.vehicleservice && (
                      <Text style={styles.errorText}>
                        {
                          (errors.serviceNeed as any)[index]?.vehicleservice
                            ?.message
                        }
                      </Text>
                    )}
                  {index > 0 && (
                    <TouchableOpacity
                      style={styles.removeServiceButton}
                      onPress={() => removeService(index)}>
                      <Text style={styles.removeServiceText}>
                        {AppStrings.MCX_REMOVE_SERVICE_TEXT}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>

                {watch(`serviceNeed.${index}.vehicleservice`) && (
                  <View style={styles.dropdownContainer}>
                    <Text style={styles.dropdownLabel}>
                      {AppStrings.MCX_SUB_SERVICE_LABEL}
                    </Text>
                    <View style={styles.subServiceList}>
                      {watch(`serviceNeed.${index}.subServices`)?.map(
                        (subService: any, subIndex: number) => (
                          <View
                            key={`${subService.id}-${subIndex}`}
                            style={styles.selectedSubService}>
                            <Text style={styles.selectedSubServiceText}>
                              {subService.name}
                            </Text>
                            <TouchableOpacity
                              style={styles.removeSubServiceButton}
                              onPress={() =>
                                removeSubService(index, subService.id)
                              }>
                              <Text style={styles.removeSubServiceText}>-</Text>
                            </TouchableOpacity>
                          </View>
                        ),
                      )}
                    </View>
                    {(() => {
                      const selectedService = watch(
                        `serviceNeed.${index}.vehicleservice`,
                      );
                      const currentSubServices =
                        watch(`serviceNeed.${index}.subServices`) || [];
                      const allSubServices = Object.keys(
                        subServiceIdNameMap || {},
                      ).filter(
                        key =>
                          subServicePriceData[key]?.service === selectedService,
                      );

                      if (!allSubServices || allSubServices.length === 0) {
                        return null;
                      }

                      return (
                        <View style={styles.subServiceCheckboxContainer}>
                          {allSubServices.map((subServiceId: string) => {
                            const isSelected = currentSubServices.some(
                              (selected: any) => selected.id === subServiceId,
                            );
                            const subServiceName =
                              subServiceIdNameMap[subServiceId] || subServiceId;

                            return (
                              <TouchableOpacity
                                key={subServiceId}
                                style={styles.subServiceCheckboxItem}
                                onPress={() => {
                                  if (isSelected) {
                                    removeSubService(index, subServiceId);
                                  } else {
                                    const subServiceData =
                                      subServicePriceData[subServiceId];
                                    if (subServiceData) {
                                      const subService = {
                                        id: subServiceId,
                                        name: subServiceName,
                                      };
                                      addSubService(index, subService);

                                      // Auto-populate flat price if applicable
                                      const priceType =
                                        subServiceData?.['price-type'] ||
                                        subServiceData?.price_type ||
                                        subServiceData?.priceType;
                                      if (priceType === 'flat') {
                                        // Calculate subIndex immediately after adding
                                        setTimeout(() => {
                                          const currentSubServices =
                                            watch(
                                              `serviceNeed.${index}.subServices`,
                                            ) || [];
                                          const subIndex =
                                            currentSubServices.findIndex(
                                              (s: any) => s.id === subServiceId,
                                            );
                                          if (subIndex !== -1) {
                                            const fixedPrice =
                                              subServiceData['min-price'] ||
                                              subServiceData.minPrice ||
                                              subServiceData.min_rate ||
                                              0;
                                            const bidKey = `${index}-${subIndex}`;
                                            setBidRates(prev => ({
                                              ...prev,
                                              [bidKey]: fixedPrice.toString(),
                                            }));
                                          }
                                        }, 100);
                                      }
                                    }
                                  }
                                }}>
                                <View
                                  style={[
                                    styles.checkbox,
                                    isSelected && styles.checkboxSelected,
                                  ]}>
                                  {isSelected && (
                                    <Text style={styles.checkboxCheckmark}>
                                      ✓
                                    </Text>
                                  )}
                                </View>
                                <Text style={styles.subServiceCheckboxText}>
                                  {subServiceName}
                                </Text>
                              </TouchableOpacity>
                            );
                          })}
                        </View>
                      );
                    })()}
                    {errors.serviceNeed &&
                      (errors.serviceNeed as any)[index]?.subServices && (
                        <Text style={styles.errorText}>
                          {
                            (errors.serviceNeed as any)[index]?.subServices
                              ?.message
                          }
                        </Text>
                      )}
                  </View>
                )}
              </View>
            </View>
          ))}

          {(() => {
            const selectedServiceIds = watchedServiceNeed
              .map((s: any) => s.vehicleservice)
              .filter(Boolean);

            const availableServicesCount = Object.keys(
              serviceIdNameMap || {},
            ).length;
            const hasAvailableServices =
              selectedServiceIds.length < availableServicesCount;

            return (
              hasAvailableServices && (
                <TouchableOpacity
                  style={styles.addServiceButton}
                  onPress={() =>
                    appendService({vehicleservice: '', subServices: []})
                  }>
                  <Text style={styles.addServiceText}>
                    {AppStrings.MCX_ADD_ANOTHER_SERVICE_TEXT}
                  </Text>
                </TouchableOpacity>
              )
            );
          })()}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {AppStrings.MCX_SCREENING_DATA_TITLE}
          </Text>
          {Object.keys(screeningQuestions)
            .sort((a, b) => {
              const numA = parseInt(a.replace('que-', ''), 10);
              const numB = parseInt(b.replace('que-', ''), 10);
              return numA - numB;
            })
            .map(key => {
              const questionData = screeningQuestions[key];
              if (!questionData || questionData.hide) {
                return null;
              }
              if (key === 'que-2') {
                const que1Answer =
                  screeningAnswers['que-1']?.toLowerCase() || '';
                if (que1Answer !== 'yes') {
                  return null;
                }
              }
              if (key === 'que-6') {
                const rideShareAnswer =
                  screeningAnswers['que-5']?.toLowerCase() || '';
                if (rideShareAnswer !== 'yes') {
                  return null;
                }
              }
              if (questionData.conditional) {
                const conditionKey = questionData.conditional.dependsOn;
                const conditionValue = questionData.conditional.value;
                if (screeningAnswers[conditionKey] !== conditionValue) {
                  return null;
                }
              }

              const hasError = errors[key] !== undefined;

              return (
                <View key={key} style={styles.dropdownContainer}>
                  <View style={styles.questionHeader}>
                    <Text style={styles.dropdownLabel}>
                      {questionData.question}
                    </Text>
                  </View>
                  {questionData.type === 'entry' ? (
                    <TextInput
                      style={[
                        styles.screeningTextInput,
                        hasError ? styles.errorInput : undefined,
                      ]}
                      placeholder={questionData.error || 'Type here'}
                      placeholderTextColor="#999999"
                      value={screeningAnswers[key] || ''}
                      onChangeText={text => {
                        setScreeningAnswers(prev => ({...prev, [key]: text}));
                        setValue(key, text);
                        trigger(key);
                      }}
                      keyboardType="default"
                      multiline={false}
                      autoCapitalize="words"
                    />
                  ) : (
                    <CommonDropdown
                      data={(questionData.options || []).map(
                        (option: string) => ({
                          label: option,
                          value: option.toLowerCase(),
                        }),
                      )}
                      value={screeningAnswers[key] || null}
                      onValueChange={value => {
                        setScreeningAnswers(prev => ({...prev, [key]: value}));
                        setValue(key, value);
                        trigger(key);
                      }}
                      placeholder="Choose option"
                      style={hasError ? styles.errorDropdown : undefined}
                    />
                  )}
                  {hasError && (
                    <Text style={styles.errorText}>
                      {typeof (errors[key] as any)?.message === 'string'
                        ? (errors[key] as any)?.message
                        : questionData?.error || 'Type here'}
                    </Text>
                  )}
                </View>
              );
            })}
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {AppStrings.MCX_ADD_YOUR_OWN_PRICE_MODEL_TEXT}
          </Text>
          <View style={styles.priceModelTable}>
            <View style={styles.priceModelHeaderRow}>
              <Text
                style={[
                  styles.priceModelHeaderCell,
                  styles.priceModelHeaderCellService,
                ]}>
                {AppStrings.MCX_PRICE_MODEL_SERVICE_NAME}
              </Text>
              <Text
                style={[
                  styles.priceModelHeaderCell,
                  styles.priceModelHeaderCellRange,
                ]}>
                {AppStrings.MCX_PRICE_MODEL_PRICE_RANGE}
              </Text>
              <Text
                style={[
                  styles.priceModelHeaderCell,
                  styles.priceModelHeaderCellBid,
                ]}>
                {AppStrings.MCX_PRICE_MODEL_BID_RATE}
              </Text>
            </View>
            {serviceFields
              .map((field, index) => {
                const vehicleservice = watch(
                  `serviceNeed.${index}.vehicleservice`,
                );
                const subServices =
                  watch(`serviceNeed.${index}.subServices`) || [];
                const serviceName =
                  (serviceIdNameMap || {})[vehicleservice] ||
                  vehicleservice ||
                  '';

                return subServices.map((subService: any, subIndex: number) => {
                  const subServiceData =
                    subServicePriceData[subService.id] || {};
                  const minPrice =
                    subServiceData['min-price'] ||
                    subServiceData.minPrice ||
                    subServiceData.min_rate ||
                    null;
                  const maxPrice =
                    subServiceData['max-price'] ||
                    subServiceData.maxPrice ||
                    subServiceData.max_rate ||
                    null;
                  let priceRange = '';
                  if (minPrice !== null && maxPrice !== null) {
                    priceRange = `$${minPrice} - $${maxPrice}`;
                  } else if (minPrice !== null) {
                    priceRange = `$${minPrice}`;
                  } else {
                    priceRange =
                      subServiceData.rate || subServiceData.price || '';
                    if (priceRange !== '') {
                      priceRange = `$${priceRange}`;
                    }
                  }
                  const bidKey = `${index}-${subIndex}`;
                  const bidRate = bidRates[bidKey] || '';
                  const bid = parseFloat(bidRate);
                  const hasError =
                    bidRate &&
                    minPrice &&
                    maxPrice &&
                    (bid < parseFloat(minPrice) || bid > parseFloat(maxPrice));

                  return (
                    <View key={`${field.id}-${subService.id}`}>
                      <View style={styles.priceModelRow}>
                        <Text
                          style={[
                            styles.priceModelCell,
                            styles.priceModelCellService,
                          ]}>
                          {serviceName} - {subService.name}
                        </Text>
                        <Text
                          style={[
                            styles.priceModelCell,
                            styles.priceModelCellRange,
                          ]}>
                          {priceRange}
                        </Text>
                        <TextInput
                          style={[
                            styles.priceModelCell,
                            styles.priceModelInput,
                            (hasError ||
                              (bidRate.trim() === '' &&
                                subServiceData?.['price-type'] !== 'flat' &&
                                subServiceData?.price_type !== 'flat' &&
                                subServiceData?.priceType !== 'flat')) &&
                              styles.errorInput,
                            (subServiceData?.['price-type'] === 'flat' ||
                              subServiceData?.price_type === 'flat' ||
                              subServiceData?.priceType === 'flat') &&
                              styles.disabledInput,
                          ]}
                          keyboardType="numeric"
                          value={bidRate}
                          onChangeText={text => {
                            const priceType =
                              subServiceData?.['price-type'] ||
                              subServiceData?.price_type ||
                              subServiceData?.priceType;
                            if (priceType === 'flat') return; // Prevent editing for flat bids
                            const newBidRates = {...bidRates, [bidKey]: text};
                            setBidRates(newBidRates);

                            // Clear error for this field when user starts typing
                            if (bidErrors[bidKey]) {
                              const newErrors = {...bidErrors};
                              delete newErrors[bidKey];
                              setBidErrors(newErrors);
                            }

                            // Clear existing timeout for this key
                            if (bidValidationTimeouts[bidKey]) {
                              clearTimeout(bidValidationTimeouts[bidKey]);
                            }

                            // Set new timeout to validate after user stops typing
                            const timeoutId = setTimeout(() => {
                              const bid = parseFloat(text);
                              const minPrice = parseFloat(
                                subServiceData['min-price'] ||
                                  subServiceData.minPrice ||
                                  '0',
                              );
                              const maxPrice = parseFloat(
                                subServiceData['max-price'] ||
                                  subServiceData.maxPrice ||
                                  '0',
                              );

                              if (
                                text &&
                                minPrice &&
                                maxPrice &&
                                (bid < minPrice || bid > maxPrice)
                              ) {
                                setBidErrors(prev => ({
                                  ...prev,
                                  [bidKey]: `Please enter an amount between $${minPrice} - ${maxPrice}`,
                                }));
                              }

                              // Clean up timeout reference
                              setBidValidationTimeouts(prev => {
                                const newTimeouts = {...prev};
                                delete newTimeouts[bidKey];
                                return newTimeouts;
                              });
                            }, 1000); // 1 second delay

                            setBidValidationTimeouts(prev => ({
                              ...prev,
                              [bidKey]: timeoutId,
                            }));
                          }}
                          placeholder=""
                          placeholderTextColor="#999999"
                          editable={
                            (subServiceData?.['price-type'] ||
                              subServiceData?.price_type ||
                              subServiceData?.priceType) !== 'flat'
                          }
                        />
                      </View>
                      {hasError && bidErrors[bidKey] && (
                        <Text style={styles.bidErrorText}>
                          {bidErrors[bidKey]}
                        </Text>
                      )}
                      {bidRate.trim() === '' &&
                        (subServiceData?.['price-type'] ||
                          subServiceData?.price_type ||
                          subServiceData?.priceType) !== 'flat' && (
                          <Text style={styles.bidErrorText}>
                            Bid amount is required
                          </Text>
                        )}
                    </View>
                  );
                });
              })
              .flat()}
            <View style={styles.priceModelTotalRow}>
              <Text
                style={[
                  styles.priceModelTotalText,
                  styles.priceModelTotalTextService,
                ]}>
                {AppStrings.MCX_PRICE_MODEL_TOTAL}
              </Text>
              <Text
                style={[
                  styles.priceModelTotalText,
                  styles.priceModelTotalTextSpacer,
                ]}
              />
              <Text
                style={[
                  styles.priceModelTotalText,
                  styles.priceModelTotalTextValue,
                ]}>
                ${' '}
                {Object.values(bidRates)
                  .reduce((acc, val) => acc + (parseFloat(val) || 0), 0)
                  .toFixed(2)}
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {AppStrings.MCX_SELECT_TIME_TEXT}
          </Text>
          <View style={styles.dropdownContainer}>
            <Text style={styles.dropdownLabel}>
              {AppStrings.MCX_ALERTS_AND_CONFIRMATION_TEXT}
            </Text>
            <Controller
              control={control}
              name="alertconfirmation"
              render={({field: {onChange, value}}) => (
                <CommonDropdown
                  data={alertConfirmationData}
                  value={value}
                  onValueChange={val => {
                    onChange(val);
                  }}
                  placeholder="Select alert and confirmation"
                  loading={isLoadingAlerts}
                />
              )}
            />
            {errors.alertconfirmation && (
              <Text style={styles.errorText}>
                {String((errors.alertconfirmation as any)?.message || '')}
              </Text>
            )}
          </View>
          <View style={styles.dropdownContainer}>
            <Text style={styles.dropdownLabel}>{AppStrings.MCX_DATE_TEXT}</Text>
            <TouchableOpacity
              style={styles.dateInput}
              onPress={() => {setShowDatePicker(true);  trigger('scheduledate');}}>
              <Text
                style={[
                  styles.dateInputText,
                  !date && styles.dateInputPlaceholder,
                ]}>
                {date || 'MM/DD/YYYY'}
              </Text>
            </TouchableOpacity>
            {errors.scheduledate && (
              <Text style={styles.errorText}>
                {String((errors.scheduledate as any)?.message ||  'Choose date for service')}
              </Text>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            {AppStrings.MCX_NOTES_AND_IMAGES}
          </Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder={AppStrings.MCX_NOTES_PLACEHOLDER_TEXT}
            value={notes}
            onChangeText={setNotes}
            multiline
          />
          <View>
            <TouchableOpacity
              style={styles.imageButton}
              onPress={_handleImagePick}>
              <Image
                source={AppCommonIcons.MCX_IMAGE_UPLOAD_ICON}
                style={styles.imageIcon}
              />
            </TouchableOpacity>

            <View style={styles.imageGrid}>
              {selectedImages.map((img, idx) => (
                <View key={`image-${idx}`} style={styles.imageContainer}>
                  <Image
                    source={{uri: img.uri}}
                    style={styles.thumbnailImage}
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    style={styles.deleteButton}
                    onPress={() => handleDeleteImage(idx)}>
                    <Image
                      source={require('../../../assets/common_icons/delete.png')}
                      style={styles.deleteIcon}
                    />
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          </View>
        </View>

        <TouchableOpacity style={styles.cancelButton} onPress={handleCancel}>
          <Text style={styles.cancelButtonText}>
            {AppStrings.MCX_CANCEL_SCHEDULE_TEXT}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.confirmButton}
          onPress={handleSubmitWithValidation}>
          <Text style={styles.confirmButtonText}>
            {AppStrings.MCX_SCHEDULE_NOW_TEXT}
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {Platform.OS === 'ios'
        ? showDatePicker && (
            <DateTimePicker
              value={datePickerValue}
              mode="date"
              display="spinner"
              onChange={handleDateChange}
              style={{width: '100%', backgroundColor: 'white'}}
            />
          )
        : showDatePicker && (
            <DateTimePicker
              value={datePickerValue}
              mode="date"
              display="calendar"
              onChange={handleDateChange}
              minimumDate={new Date()}
              onTouchCancel={() => setShowDatePicker(false)}
            />
          )}
      <LoaderOverlay visible={_isLoading} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingBottom: 100, // Add padding to account for bottom bar height
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#1f2a3a',
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
    transform: [{rotate: '180deg'}],
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 24,
  },
  scrollContent: {
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
    marginBottom: 12,
  },
  imageContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    marginRight: 8,
    marginBottom: 8,
    borderRadius: 8,
  },
  imageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
    gap: 8,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  deleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.PRIMARY,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  deleteIcon: {
    width: 12,
    height: 12,
    tintColor: '#FFFFFF',
  },

  dropdownContainer: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingVertical: 12,
    paddingHorizontal: 12,
    marginBottom: 8,
  },
  dropdownLabel: {
    fontWeight: 'bold',
    color: '#8B0000', // Dark red color to match the design
    marginBottom: 8,
    fontSize: 14,
    flex: 1,
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  validIndicator: {
    color: '#4CAF50',
    fontSize: 16,
    fontWeight: 'bold',
  },
  input: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    padding: 12,
    fontSize: 16,
    color: '#333333',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  bidErrorText: {
    color: '#FF6B6B',
    fontSize: 12,
    fontWeight: '600',
    marginTop: 4,
    marginLeft: 8,
    marginBottom: 8,
  },
  screeningTextInput: {
    backgroundColor: '#FFFFFF',
    borderRadius: 4,
    paddingVertical: 10,
    paddingHorizontal: 12,
    fontSize: 14,
    color: '#000000',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    minHeight: 44,
  },
  locationInputContainer: {
    marginBottom: 8,
    zIndex: 1000,
  },
  locationInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingHorizontal: 12,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.COMMON_BlACK_SHADE,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    zIndex: 1000,
  },
  locationInputPlaceholder: {
    color: '#6B7280', // Gray color for placeholder text
  },
  locationInputText: {
    color: Colors.COMMON_BlACK_SHADE,
  },
  locationListView: {
    zIndex: 1000,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
  },
  locationRow: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    padding: 13,
    height: 44,
    flexDirection: 'row',
  },
  locationDescription: {
    color: Colors.COMMON_BlACK_SHADE,
  },
  locationDisplay: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    marginTop: 8,
  },
  locationText: {
    fontSize: 16,
    color: Colors.COMMON_BlACK_SHADE,
  },
  optionText: {
    fontSize: 14,
    color: Colors.COMMON_BlACK_SHADE,
  },
  radioGroup: {
    marginBottom: 12,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 8,
    padding: 12,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    justifyContent: 'space-between',
  },
  radioCircle: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.COMMON_BlACK_SHADE,
    marginRight: 8,
  },
  radioCircleSelected: {
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 6,
    borderColor: Colors.PRIMARY,
    marginRight: 8,
  },
  priceModelTable: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 8,
  },
  priceModelHeaderRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: Colors.PRIMARY,
    paddingBottom: 4,
  },
  priceModelHeaderCell: {
    fontWeight: 'bold',
    color: 'red',
    fontSize: 14,
  },
  priceModelRow: {
    flexDirection: 'row',
    paddingVertical: 8,
  },
  priceModelCell: {
    fontSize: 14,
    color: Colors.TEXT_COLOR,
  },
  priceModelTotalRow: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: Colors.PRIMARY,
    paddingTop: 8,
  },
  priceModelTotalText: {
    fontWeight: 'bold',
    color: 'red',
    fontSize: 14,
  },
  imageButton: {
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: Colors.SECONDARY,
    alignItems: 'center',
    justifyContent: 'center',
    width: 48,
    height: 48,
  },
  imageIcon: {
    width: 24,
    height: 24,
    tintColor: Colors.COMMON_WHITE_SHADE,
  },
  cancelButton: {
    backgroundColor: Colors.BACKGROUND,
    borderWidth: 2,
    borderColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 16,
  },
  cancelButtonText: {
    color: Colors.COMMON_BlACK_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  confirmButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 14,
    borderRadius: 4,
    alignItems: 'center',
    marginBottom: 32,
  },
  confirmButtonText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#FF0000',
    fontWeight: '600',
    fontSize: 14,
    marginTop: 4,
    marginBottom: 4,
  },
  servicesTitle: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 18,
    marginBottom: 12,
    backgroundColor: '#9b0a0f',
    padding: 8,
  },
  serviceItem: {
    marginBottom: 16,
  },
  removeServiceButton: {
    backgroundColor: '#ff4444',
    padding: 8,
    borderRadius: 4,
    marginTop: 8,
    alignItems: 'center',
  },
  removeServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
  },
  subServiceContainer: {
    marginTop: 8,
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    paddingVertical: 12,
    paddingLeft: 12,
  },
  addServiceButton: {
    backgroundColor: Colors.PRIMARY,
    padding: 12,
    borderRadius: 4,
    alignItems: 'center',
    marginTop: 8,
  },
  addServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  saveLocationButton: {
    marginTop: 8,
    padding: 8,
    borderRadius: 4,
    backgroundColor: Colors.PRIMARY,
    alignItems: 'center',
    justifyContent: 'center',
    width: 160,
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    opacity: 0.6,
  },
  saveLocationText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontWeight: 'bold',
    fontSize: 16,
  },
  disabledText: {
    color: '#666666',
  },
  disabledInput: {
    backgroundColor: '#f0f0f0',
    color: '#666666',
  },
  errorInput: {
    borderColor: '#FF0000',
    borderWidth: 2,
  },
  errorDropdown: {
    borderColor: '#FF0000',
    borderWidth: 2,
    borderRadius: 4,
  },
  dateInput: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    minHeight: 40,
    justifyContent: 'center',
  },
  dateInputText: {
    fontSize: 16,
    color: '#000000',
    fontWeight: '400',
  },
  dateInputPlaceholder: {
    color: '#999',
    fontWeight: '400',
  },
  availableText: {
    color: 'green',
  },
  notAvailableText: {
    color: 'red',
  },
  rateText: {
    marginTop: 4,
    color: '#444',
    fontSize: 14,
  },
  priceModelHeaderCellService: {
    flex: 3,
  },
  priceModelHeaderCellRange: {
    flex: 2,
  },
  priceModelHeaderCellBid: {
    flex: 2,
  },
  priceModelCellService: {
    flex: 3,
  },
  priceModelCellRange: {
    flex: 2,
  },
  priceModelInput: {
    flex: 2,
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    paddingHorizontal: 8,
  },
  priceModelTotalTextService: {
    flex: 3,
  },
  priceModelTotalTextSpacer: {
    flex: 2,
  },
  priceModelTotalTextValue: {
    flex: 2,
  },
  subServiceCheckboxContainer: {
    marginTop: 12,
  },
  subServiceCheckboxItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 4,
    borderWidth: 2,
    borderColor: Colors.COMMON_BlACK_SHADE,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  checkboxSelected: {
    backgroundColor: Colors.PRIMARY,
    borderColor: Colors.PRIMARY,
  },
  checkboxCheckmark: {
    color: Colors.COMMON_WHITE_SHADE,
    fontSize: 14,
    fontWeight: 'bold',
  },
  subServiceCheckboxText: {
    fontSize: 14,
    color: Colors.COMMON_BlACK_SHADE,
    flex: 1,
  },
  subServiceList: {
    marginTop: 8,
  },
  selectedSubService: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f0f0f0',
    padding: 8,
    borderRadius: 4,
    marginBottom: 4,
  },
  selectedSubServiceText: {
    fontSize: 14,
    color: Colors.COMMON_BlACK_SHADE,
    flex: 1,
  },
  removeSubServiceButton: {
    backgroundColor: '#ff4444',
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  removeSubServiceText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  loaderContainer: {
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    marginTop: 8,
    fontSize: 14,
    color: '#8B0000',
  },
});

export default Schedule;
