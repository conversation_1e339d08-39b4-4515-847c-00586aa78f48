import {Colors, Fonts, Sizes} from '../../../../utils/constants/Theme';
import {AppStrings} from '../../../../utils/constants/AppStrings';
import {Animated, StyleSheet, Text, View} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import React from 'react';

interface VinDetailsComponentProps {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
}

const VinDetailsComponent: React.FC<VinDetailsComponentProps> = ({
  vin,
  make,
  model,
  manufactureYear,
  fuel,
}) => {
  const [fadeAnim] = React.useState(new Animated.Value(0));

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();
  }, []);

  const renderDetailRow = (
    icon: string,
    label: string,
    value: string,
    color?: string,
  ) => (
    <View style={vinDetailsModernStyles.vinDetailRow}>
      <View style={vinDetailsModernStyles.vinDetailLeft}>
        <Icon
          name={icon}
          size={20}
          color={color || '#A10000'}
          style={vinDetailsModernStyles.vinDetailIcon}
        />
        <Text style={vinDetailsModernStyles.vinDetailLabel}>{label}:</Text>
      </View>
      <Text style={[vinDetailsModernStyles.vinDetailValue, color && {color}]}>
        {value}
      </Text>
    </View>
  );

  return (
    <Animated.View
      style={[vinDetailsModernStyles.vinContainer, {opacity: fadeAnim}]}>
      <View style={vinDetailsModernStyles.vinDetailsCard}>
        <View style={vinDetailsModernStyles.vinCardHeader}>
          <Icon name="car-info" size={26} color="#A10000" />
          <Text style={vinDetailsModernStyles.vinCardTitle}>
            {AppStrings.MCX_VEHICLE_INFO_TITLE}
          </Text>
        </View>

        {renderDetailRow('identifier', AppStrings.MCX_VIN_LABEL, vin)}
        {renderDetailRow('car', AppStrings.MCX_MAKE_LABEL, make)}
        {renderDetailRow('car-side', AppStrings.MCX_MODEL_LABEL, model)}
        {renderDetailRow(
          'calendar',
          AppStrings.MCX_YEAR_LABEL,
          manufactureYear,
        )}
        {renderDetailRow('gas-station', AppStrings.MCX_FUEL_TYPE_LABEL, fuel)}
      </View>
    </Animated.View>
  );
};

// Modern styles for VIN Details Component
const vinDetailsModernStyles = StyleSheet.create({
  vinContainer: {
    marginHorizontal: 20,
    marginTop: 8,
    marginBottom: 20,
  },
  vinDetailsCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 20,
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
  },
  vinCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  vinCardTitle: {
    fontSize: 17,
    fontWeight: '700',
    color: '#374151',
    marginLeft: 12,
    fontFamily: Fonts.ROBO_BOLD,
    letterSpacing: 0.3,
  },
  vinDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 14,
    paddingVertical: 4,
  },
  vinDetailLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  vinDetailIcon: {
    marginRight: 10,
  },
  vinDetailLabel: {
    fontSize: 15,
    color: '#6B7280',
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
  },
  vinDetailValue: {
    fontSize: 15,
    color: '#1F2937',
    fontFamily: Fonts.ROBO_BOLD,
    fontWeight: '700',
    letterSpacing: 0.2,
  },
});

export default VinDetailsComponent;
