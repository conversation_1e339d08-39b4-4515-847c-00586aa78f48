import React from 'react';
import {View, Text, StyleSheet, ScrollView} from 'react-native';
import CommonTextInput from '../../../../components/common/CommonTextInput';
import CustomButton from '../../../../components/common/CustomButton';
import RegistrationTitleSection from '../../../../components/common/RegistrationTitleSection';
import {Colors, Fonts} from '../../../../utils/constants/Theme';
import {AppStrings} from '../../../../utils/constants/AppStrings';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

interface RegistrationPasswordScreenProps {
  personalData: {
    firstName: string;
    lastName: string;
    email: string;
    mobile: string;
  };
  passwordData: {password: string; confirmPassword: string};
  setPasswordData: (data: {password: string; confirmPassword: string}) => void;
  setActiveTab: (tab: string) => void;
}

const RegistrationPasswordScreen: React.FC<RegistrationPasswordScreenProps> = ({
  personalData: _personalData,
  passwordData,
  setPasswordData,
  setActiveTab,
}) => {
  const [errors, setErrors] = React.useState<{
    password?: string;
    confirmPassword?: string;
  }>({});
  const [isContinuing, setIsContinuing] = React.useState(false);

  const insets = useSafeAreaInsets();

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    if (password.length < minLength) {
      return 'Password must be at least 8 characters.';
    }
    if (!hasUpperCase) {
      return 'Password must contain at least one uppercase letter.';
    }
    if (!hasLowerCase) {
      return 'Password must contain at least one lowercase letter.';
    }
    if (!hasNumbers) {
      return 'Password must contain at least one number.';
    }
    if (!hasSpecialChar) {
      return 'Password must contain at least one special character.';
    }
    return '';
  };

  const handlePasswordChange = (text: string) => {
    const passwordError = validatePassword(text);
    setErrors(prevErrors => ({
      ...prevErrors,
      password: passwordError,
    }));
    setPasswordData({...passwordData, password: text});
  };

  const handleConfirmPasswordChange = (text: string) => {
    let confirmError = '';
    if (!text) {
      confirmError = 'Confirm your password.';
    } else if (text !== passwordData.password) {
      confirmError = 'Passwords do not match.';
    }
    setErrors(prevErrors => ({
      ...prevErrors,
      confirmPassword: confirmError,
    }));
    setPasswordData({...passwordData, confirmPassword: text});
  };

  const handleContinue = async () => {
    const passwordError = validatePassword(passwordData.password);
    const confirmError = !passwordData.confirmPassword
      ? 'Confirm your password.'
      : passwordData.password !== passwordData.confirmPassword
      ? 'Passwords do not match.'
      : '';
    if (passwordError || confirmError) {
      setErrors({
        password: passwordError,
        confirmPassword: confirmError,
      });
      return;
    }
    setActiveTab('3');
    setIsContinuing(true);
  };

  const BUTTON_HEIGHT = 60 + (insets.bottom > 0 ? insets.bottom : 16);

  return (
    <View style={passwordModernStyles.outerContainer}>
      <ScrollView
        style={passwordModernStyles.scrollView}
        contentContainerStyle={[
          passwordModernStyles.scrollViewContent,
          {paddingBottom: BUTTON_HEIGHT + 40}
        ]}
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        bounces={false}>
        <View style={passwordModernStyles.formSection}>
          <RegistrationTitleSection
            title={AppStrings.MCX_CREATE_PASSWORD_TITLE}
            backgroundColor="#FFFFFF"
            borderBottomWidth={1}
            borderBottomColor={Colors.COMMON_GREY_SHADE_LIGHT}
            paddingVertical={18}
            paddingHorizontal={0}
          />

          <View style={passwordModernStyles.inputContainer}>
            <Text style={passwordModernStyles.inputLabel}>
              {AppStrings.MCX_PASSWORD_LABEL}
            </Text>
            <CommonTextInput
              value={passwordData.password}
              onChangeText={handlePasswordChange}
              placeholder={AppStrings.MCX_PASSWORD_PLACEHOLDER}
              style={[
                passwordModernStyles.textInput,
                errors.password && passwordModernStyles.inputError,
              ]}
              placeholderTextColor="#9CA3AF"
              secureTextEntry={true}
            />
            {errors.password && (
              <Text style={passwordModernStyles.errorText}>
                {errors.password}
              </Text>
            )}
          </View>

          <View style={passwordModernStyles.inputContainer}>
            <Text style={passwordModernStyles.inputLabel}>
              {AppStrings.MCX_CONFIRM_PASSWORD_LABEL}
            </Text>
            <CommonTextInput
              value={passwordData.confirmPassword}
              onChangeText={handleConfirmPasswordChange}
              placeholder={AppStrings.MCX_CONFIRM_PASSWORD_PLACEHOLDER}
              style={[
                passwordModernStyles.textInput,
                errors.confirmPassword && passwordModernStyles.inputError,
              ]}
              placeholderTextColor="#9CA3AF"
              secureTextEntry={true}
            />
            {errors.confirmPassword && (
              <Text style={passwordModernStyles.errorText}>
                {errors.confirmPassword}
              </Text>
            )}
          </View>
        </View>
      </ScrollView>

      <View
        style={[
          passwordModernStyles.fixedBottomContainer,
          {
            paddingBottom: insets.bottom > 0 ? insets.bottom : 16,
          }
        ]}>
        <CustomButton
          text={AppStrings.MCX_CONTINUE_BUTTON}
          onPress={handleContinue}
          variant="primary"
          size="large"
          fullWidth={true}
          textColor="#fff"
          backgroundColor={Colors.SECONDARY}
          isBoldText={true}
          isBottomButton={true}
          bottomLineWidth={1}
          bottomLineColor={Colors.COMMON_GREY_SHADE_LIGHT}
          disabled={isContinuing}
        />
      </View>
    </View>
  );
};

const passwordModernStyles = StyleSheet.create({
  outerContainer: {
    flex: 1,
    position: 'relative',
  },
  scrollView: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingHorizontal: 0,
  },
  formSection: {
    backgroundColor: '#FFFFFF',
    marginHorizontal: 16,
    marginTop: 16,
    borderRadius: 12,
    alignSelf: 'stretch',
    minHeight: 'auto',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  inputContainer: {
    marginBottom: 14,
    paddingHorizontal: 10,
    paddingTop: 10,
  },
  inputLabel: {
    fontSize: 15,
    fontWeight: '600',
    color: '#374151',
    fontFamily: Fonts.ROBO_REGULAR,
    marginBottom: 10,
    letterSpacing: 0.3,
  },
  textInput: {
    borderWidth: 1.5,
    borderColor: '#E5E7EB',
    borderRadius: 10,
    fontSize: 15,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#F9FAFB',
    paddingVertical: 14,
    paddingHorizontal: 16,
  },
  inputError: {
    borderColor: '#DC2626',
    borderWidth: 1.5,
  },
  errorText: {
    color: '#DC2626',
    fontSize: 13,
    marginTop: 6,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '500',
  },
  fixedBottomContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#FFFFFF',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
});

export default RegistrationPasswordScreen;