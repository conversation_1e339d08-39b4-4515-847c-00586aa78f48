// ConfirmAppointment.tsx
import React, {useState, useEffect,useCallback} from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  SafeAreaView,
  Platform,
  StatusBar,
  BackHandler
} from 'react-native';
import {launchImageLibrary, Asset} from 'react-native-image-picker';
import {useNavigation, useRoute, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RootStackParamList} from '../../../utils/configs/types';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {VehicleService} from '../../../utils/services/VehicleService';
import {AppointmentService} from '../../../utils/services/AppointmentService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_CUSTOMER_ID} from '../../../utils/globals';
import {child, set, get} from '@react-native-firebase/database';
import Icon from 'react-native-vector-icons/Ionicons';
import LoaderOverlay from '../../../components/common/LoaderOverlay';
import {AppStrings,RouteNames,AppCommonIcons} from '../../../utils/constants/AppStrings';
import AppBackground from '../../../components/ui/AppBackground';
import {Colors, Fonts, Sizes} from '../../../utils/constants/Theme';
import { CommonActions } from '@react-navigation/native';

type ConfirmAppointmentNavigationProp = StackNavigationProp<
  RootStackParamList,
  'ConfirmAppointment'
>;
type ConfirmAppointmentRouteProp = RouteProp<
  RootStackParamList,
  'ConfirmAppointment'
>;

const ConfirmAppointment: React.FC = () => {
  const navigation = useNavigation<ConfirmAppointmentNavigationProp>();
  const route = useRoute<ConfirmAppointmentRouteProp>();
  const {'appointment-detail': appointment,initialTab} = route.params;
  const savedInitialTab =initialTab?? 0;
  const mechanicId = appointment.details['mechanic'];
  const workRequestId = appointment['work-request-id'];
  const appointmentId =
    typeof appointment['appointment-id'] === 'string'
      ? appointment['appointment-id']
      : appointment['work-request-id'];
  const insets = useSafeAreaInsets();

  const [notes, setNotes] = useState('');
  const [uploadfile, setUploadfile] = useState<Asset[]>([]);
  const [mechanicDetail, setMechanicDetail] = useState<any>(null);
  const [appointmentPriceModel, setAppointmentPriceModel] = useState<any>(null);
  const [appointmentInfo, setAppointmentInfo] = useState<any>(null);
  const [mechLocation, setMechLocation] = useState({
    city: '',
    country: '',
    address1: '',
  });
  const [isCancelLoading, setIsCancelLoading] = useState(false);
  const [isApproveLoading, setIsApproveLoading] = useState(false);
  const [hasMechanicBid, setHasMechanicBid] = useState(false);
  const [isProcessingComplete, setIsProcessingComplete] = useState(false);
  const [isAlreadyApproved, setIsAlreadyApproved] = useState(false);

  useEffect(() => {
    loadMechanicDetails(mechanicId);
    prepareServicePriceModel(workRequestId);
    loadAppointmentInfo(appointmentId);
  }, [mechanicId, workRequestId, appointmentId]);

  const loadMechanicDetails = async (mechanicId: string | any) => {
    try {
      if (typeof mechanicId === 'object') {
        setMechanicDetail(mechanicId);
        setMechLocation({
          city: mechanicId?.city || '',
          country: mechanicId?.country || '',
          address1: mechanicId?.address1 || '',
        });
      } else {
        const mechanicSnap = await get(
          VehicleService.getMechanicDetail(mechanicId),
        );
        const mech = mechanicSnap.val();
        setMechanicDetail(mech);
        setMechLocation({
          city: mech?.city || '',
          country: mech?.country || '',
          address1: mech?.address1 || '',
        });
      }
    } catch (error) {
      console.error('Error loading mechanic details:', error);
    }
  };

const prepareServicePriceModel = async (wrkReqId: string) => {
    try {
        // For pending requests (types: 'request'), use appointment.details.services directly
        if (appointment.types === 'request' && appointment.details?.services) {
            console.log('Processing pending request with details.services');
            
            // Get basic work request info
            const workReqSnap = await get(VehicleService.fetchWorkRequest(wrkReqId));
            const wrkReqDetails = workReqSnap.val()?.[wrkReqId] || workReqSnap.val();

            const date = appointment.details.day && appointment.details.month 
                ? `${appointment.details.day} ${appointment.details.month}` 
                : new Date().toDateString();
            
            const model = {
                services: appointment.details.services.map((service: any) => ({
                    service: service.service,
                    subservice: service.subservice.map((sub: string) => ({
                        name: sub,
                        max_price: '0',
                        min_price: '0',
                        price_type: '',
                        customer_bid: '0',
                        mechanic_bid: '0',
                    }))
                })),
                timeRange: appointment.details.time || '',
                day: appointment.details.day || new Date().getDate(),
                month: appointment.details.month || '',
                mechanic: appointment['mechanic-id'] || null,
                requestAddress: wrkReqDetails?.['request-address']?.formatted_address || '',
                requestType: wrkReqDetails?.['request-type'] || 'Pending Request',
            };
            
            setAppointmentPriceModel(model);
            setHasMechanicBid(false); // No mechanic bid for pending requests
            return;
        }

        // Original logic for approved appointments
        const workReqSnap = await get(VehicleService.fetchWorkRequest(wrkReqId));
        const wrkReqData = workReqSnap.val();
        const wrkReqDetails = wrkReqData?.[wrkReqId] || wrkReqData;

        if (!wrkReqDetails) {
            console.error('Work request details not found');
            setAppointmentPriceModel(null);
            setHasMechanicBid(false);
            return;
        }

        const customerId = wrkReqDetails.customer;
        if (customerId) {
            const userSnap = await get(VehicleService.fetchUserInformation(customerId));
            // setUserInfoDetail(userSnap.val());
        }

        const date = wrkReqDetails['request-date'];
        const dayName = new Date(date).getDate();
        const month = VehicleService.monthNames[new Date(date).getMonth()];

        const tempSer = wrkReqDetails.services;
        if (!tempSer) {
            console.error('No services found in work request');
            setAppointmentPriceModel(null);
            setHasMechanicBid(false);
            return;
        }

        const serId = Object.keys(tempSer);
        const exitServiceName: any[] = [];

        for (const sv of serId) {
            const tempSubser = wrkReqDetails.services[sv]['sub-services'];
            if (!tempSubser || !Array.isArray(tempSubser)) {
                console.error('Invalid sub-services for service:', sv);
                continue;
            }
            const exitSubServiceName: any[] = [];

            for (let ssv = 0; ssv < tempSubser.length; ssv++) {
                const subServiceSnap = await get(
                    VehicleService.fetchSubServiceName(tempSubser[ssv]),
                );
                const subVal = subServiceSnap.val()?.[tempSubser[ssv]];
                exitSubServiceName[ssv] = {
                    name: subVal?.name || '',
                    max_price: subVal?.['max-price'] || '0',
                    min_price: subVal?.['min-price'] || '0',
                    price_type: subVal?.['price-type'] || '',
                    customer_bid: wrkReqDetails.services[sv]['customer-bid']?.[ssv] || '0',
                    mechanic_bid: wrkReqDetails.services[sv]['mechanic-bid']?.[ssv] || '0',
                };
            }

            const serviceSnap = await get(VehicleService.fetchServiceName(sv));
            const serVal = serviceSnap.val()?.[sv];
            if (serVal?.name) {
                exitServiceName.push({
                    service: serVal.name,
                    subservice: exitSubServiceName,
                });
            }
        }

        if (exitServiceName.length > 0) {
            const model = {
                services: exitServiceName,
                timeRange: wrkReqDetails['request-time-range'],
                day: dayName,
                month,
                mechanic: wrkReqDetails.mechanic,
                requestAddress: wrkReqDetails['request-address']?.formatted_address || '',
                requestType: wrkReqDetails['request-type'] === 'Appointment'
                    ? 'By Appointment'
                    : wrkReqDetails['request-type'],
            };
            setAppointmentPriceModel(model);

            const hasAnyMechanicBid = exitServiceName.some((service: any) =>
                service.subservice.some((sub: any) => sub.mechanic_bid && sub.mechanic_bid !== '0')
            );
            setHasMechanicBid(hasAnyMechanicBid);
        } else {
            console.error('No valid services found');
            setAppointmentPriceModel(null);
            setHasMechanicBid(false);
        }
    } catch (error) {
        console.error('Error in prepareServicePriceModel:', error);
        setAppointmentPriceModel(null);
        setHasMechanicBid(false);
    }
};

  const loadAppointmentInfo = async (aptId: string) => {
    try {
      if (!aptId || typeof aptId !== 'string') {
        console.error('Invalid appointmentId:', aptId);
        return;
      }
      const aptSnap = await get(VehicleService.fetchAppointmentDetails(aptId));
      const aptData = aptSnap.val();
      setAppointmentInfo(aptData ? aptData[aptId] || null : null);
    } catch (error) {
      console.error('Error loading appointment info:', error);
    }
  };

  const pickImages = async () => {
    launchImageLibrary(
      {
        mediaType: 'photo',
        selectionLimit: 0,
      },
      response => {
        if (response.didCancel) {
          console.log('User cancelled image picker');
        } else if (response.errorCode) {
          console.log('ImagePicker Error: ', response.errorMessage);
        } else if (response.assets) {
          setUploadfile([...uploadfile, ...response.assets]);
        }
      },
    );
  };

  const cancelAppointment = async () => {
    setIsCancelLoading(true);
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) {
        throw new Error('Customer ID not found');
      }

      // Ensure mechanicId is a string or undefined, never an object
      const safeMechanicId = typeof mechanicId === 'string' ? mechanicId : undefined;

      // For pending requests, we might need to cancel the work request instead
      if (appointment.types === 'request') {
        if (appointment['request-type'] === 'multiple') {
          await AppointmentService.cancelMultipleWorkRequest(workRequestId);
        } else {
          await AppointmentService.cancelworkRequest(workRequestId, safeMechanicId);
        }
      } else {
        await AppointmentService.cancelAppointment(
          appointmentId,
          mechanicId,
          workRequestId,
          notes,
        );
     }

     await AppointmentService.updateCancelCount();
  
     if (uploadfile.length > 0) {
       await uploadingImage(appointmentId);
     }
  
     // Update log
     const logRef = AppointmentService.updateLog(workRequestId);
     await set(child(logRef, 'appointment/rejected-time'), Math.floor(Date.now() / 1000));
  
     setIsCancelLoading(false);
     setIsProcessingComplete(true);
      Alert.alert('Success', 'Appointment cancelled successfully', [
        {
          text: 'OK',
          onPress: () => {
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: RouteNames.MCX_NAV_DashBoard,
                    params: {
                      screen: RouteNames.MCX_NAV_MESSAGES,
                      params: {
                        initialTab: savedInitialTab,
                        refresh: true,
                        cancelledAppointment: workRequestId,
                      },
                    },
                  },
                ],
              }),
            );
          },
        },
      ]);  
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      setIsCancelLoading(false);
      setIsProcessingComplete(false);
      Alert.alert('Error', 'Failed to cancel appointment. Please try again.');
    }
  };

  const doYouWantToApproveAppointment = () => {
    Alert.alert(
      'Approve Appointment',
      'Are you sure you want to approve Appointment? Note: There is no Undo.',
      [
        {text: 'No', style: 'cancel'},
        {text: 'APPROVE', onPress: () => approveAppointment()},
      ],
    );
  };

  const approveAppointment = async () => {
    setIsApproveLoading(true);
    try {
      await AppointmentService.approveAppointment(workRequestId);
      const logRef = AppointmentService.updateLog(workRequestId);
      await set(
        child(logRef, 'appointment/approved-time'),
        Math.floor(Date.now() / 1000),
      );
      await set(
        child(logRef, 'schedule/created-time'),
        Math.floor(Date.now() / 1000),
      );
      const scheduleTime =
        appointmentInfo?.['schedule-time'] || Math.floor(Date.now() / 1000);
      await set(child(logRef, 'schedule/schedule-time'), scheduleTime);

    // setIsApproveLoading(false);
      setIsProcessingComplete(true);
      Alert.alert('Success', 'Appointment approved successfully', [
        {
          text: 'OK',
          onPress: () => {
                navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: RouteNames.MCX_NAV_DashBoard,
                    params: {
                      screen: RouteNames.MCX_NAV_MESSAGES,
                      params: {
                        initialTab: savedInitialTab,
                        refresh: true,
                        approvedAppointment: workRequestId
                      },
                    },
                  },
                ],
              }),
            );
          },
        },
      ]);
    } catch (error) {
      console.error('Error approving appointment:', error);
      setIsApproveLoading(false);
      Alert.alert('Error', 'Failed to approve appointment. Please try again.');
    }
  };

  const uploadingImage = async (aptId: string) => {
    if (!uploadfile || uploadfile.length === 0) {
      return;
    }
    const uploadArray: string[] = [];
    for (let j = 0; j < uploadfile.length; j++) {
      try {
        const snapshot = await VehicleService.uploadImage(uploadfile[j].uri!, aptId, j, 'appointmentImages');
        const profileURL = snapshot.metadata.fullPath;
        const downloadURL = await VehicleService.getDownloadURL(profileURL);
        uploadArray[j] = downloadURL;
      } catch (error) {
        console.error('Error uploading image ' + (j + 1) + ':', error);
      }
    }
    await VehicleService.updateCancelAppointmentImagePath(uploadArray, aptId);
  };

  const handleBackPress = useCallback (() => {
    const messageData = {
      'message-key': appointment['work-request-id'] || 'unknown',
      'customer-read': appointment['customer-read'] || false,
      types: appointment.types || 'services',
      'work-request-id': appointment['work-request-id'],
      'is-approved': appointment['is-approved'] || false,
      'is-paid': appointment['is-paid'] || false,
      'created-time': appointment['created-time'] || Date.now(),
      details: appointment.details || {},
      'work-status': appointment['work-status'] || 'appointment',
      status: appointment.status || 'appointment',
      'mechanic-id': appointment['mechanic-id'] || null,
    };   
    const messageArray = [messageData];
    const position = 0;
    let timeline: any[] = [];   
   (navigation as any).navigate(RouteNames.MCX_NAV_DashBoard, {
  screen: RouteNames.MCX_NAV_MESSAGE_DETAILS,
  params: {
    'message-array': messageArray,
    'position': position,
    timeline,
    'initialTab': savedInitialTab,
    'fromConfirmAppointment': true
  }
});
    
    return true; 
  },[navigation,savedInitialTab,appointment])
   useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      handleBackPress();
      return true; 
    });

    return () => backHandler.remove();
  }, [handleBackPress]);
  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor="#1f2a38" />
      <AppBackground />
      <View style={styles.header}>
        <View style={styles.navbar}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBackPress}
            activeOpacity={0.7}>
            <Icon name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Image
            source={require('../../../assets/logo/logo_b_client.png')}
            style={styles.logo}
            resizeMode="contain"
          />
          <View style={styles.backButton} />
        </View>
        <View style={styles.titleBar}>
          <Text style={styles.titleBarText}>CONFIRM APPOINTMENT</Text>
        </View>
      </View>

     <ScrollView
    style={styles.content}
     contentContainerStyle={[
    styles.contentContainer,
    {paddingBottom: insets.bottom + 100} 
  ]}
    showsVerticalScrollIndicator={false} 

    >
    {(!hasMechanicBid||!mechanicDetail || Object.keys(mechanicDetail).length === 0) && appointment.types === 'request' ? (
      <View style={styles.pendingRequestCard}>
        <View style={styles.pendingHeaderRow}>
          <View style={styles.pendingIconContainer}>
            <Icon name="time-outline" size={28} color="#FFA726" />
          </View>
          <View style={styles.pendingHeaderText}>
            <Text style={styles.pendingTitle}>Pending Request</Text>
            <Text style={styles.pendingSubtitle}>Waiting for mechanic response</Text>
          </View>
        </View>

        <View style={styles.pendingDateTimeSection}>
          <View style={styles.pendingDateBox}>
            <Text style={styles.pendingDateNumber}>
              {appointment.details?.day || '1'}
            </Text>
            <Text style={styles.pendingDateMonth}>
              {appointment.details?.month || 'Jan'}
            </Text>
          </View>
          {appointment.details?.time && (
            <View style={styles.pendingTimeBox}>
              <Icon name="time" size={16} color="#666" />
              <Text style={styles.pendingTimeText}>{appointment.details.time}</Text>
            </View>
          )}
        </View>

        <View style={styles.pendingDivider} />

        <View style={styles.pendingServicesSection}>
          <Text style={styles.pendingSectionTitle}>Requested Services</Text>
          {appointment.details?.services?.map((service: any, idx: number) => (
            <View key={idx} style={styles.pendingServiceCard}>
              <View style={styles.pendingServiceHeader}>
                <View style={styles.pendingServiceIconWrapper}>
                  <Image
                    source={AppCommonIcons.MCX_OIL_ICON}
                    style={styles.pendingServiceIcon}
                  />
                </View>
                <Text style={styles.pendingServiceName}>{service?.service || 'Service'}</Text>
              </View>
              {service?.subservice && service.subservice.length > 0 && (
                <View style={styles.pendingSubservicesList}>
                  {service.subservice.map((sub: string, subIdx: number) => (
                    <View key={subIdx} style={styles.pendingSubserviceItem}>
                      <View style={styles.pendingBullet} />
                      <Text style={styles.pendingSubserviceText}>{sub}</Text>
                    </View>
                  ))}
                </View>
              )}
            </View>
          ))}
        </View>

        <View style={styles.pendingStatusBadge}>
          <View style={styles.pendingStatusDot} />
          <Text style={styles.pendingStatusText}>Awaiting Response</Text>
        </View>
      </View>
    ) : (
      <>
        {hasMechanicBid&&mechanicDetail && Object.keys(mechanicDetail).length > 0 && (
          <>
            <Text style={styles.sectionTitle}>
              {AppStrings.MCX_MY_MECHANIC_TITLE}
            </Text>
            <View style={styles.mechanicCard}>
              <Image
                source={{
                  uri: mechanicDetail?.imageUrl,
                }}
                style={styles.profileImage}
              />
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>
                  {mechanicDetail?.['first-name'] || ''}{' '}
                  {mechanicDetail?.['last-name'] || ''}
                </Text>
                <Text style={styles.profileAddress}>
                  {mechLocation.address1}, {mechLocation.city},{' '}
                  {mechLocation.country}
                </Text>
                <Text style={styles.profileAvailability}>
                  {mechanicDetail?.availability ? 'Open' : 'Appointment'}
                </Text>
                <Text style={styles.profileRating}>
                  ⭐ {mechanicDetail?.['mechanic-rating'] || 0}/5
                </Text>
              </View>
            </View>
          </>
        )}

        <View style={styles.dateTimeContainer}>
          <Text style={styles.dateText}>
            {appointmentPriceModel?.day}{' '}
            {appointmentPriceModel?.month?.toUpperCase()}
          </Text>
          <Text style={styles.timeRange}>{appointmentPriceModel?.timeRange}</Text>
          {appointmentPriceModel?.requestType &&
          <View style={styles.timeButton}>
            <Text style={styles.timeButtonText}>
              {appointmentPriceModel?.requestType}
            </Text>
          </View>}
        </View>

        <View style={styles.tableContainer}>
          <View style={styles.tableHeader}>
            <Text style={[styles.tableCell, styles.tableHeaderText]}>
              Service Name
            </Text>
            <Text style={[styles.tableCell, styles.tableHeaderText]}>
              Price Range
            </Text>
            <Text style={[styles.tableCell, styles.tableHeaderText]}>
              Your Bid
            </Text>
            <Text style={[styles.tableCell, styles.tableHeaderText]}>
              Mechanic Bid
            </Text>
          </View>
          {appointmentPriceModel?.services.map((service: any, idx: number) =>
            service.subservice.map((sub: any, subIdx: number) => (
              <View key={`${idx}-${subIdx}`} style={styles.tableRow}>
                <View style={styles.serviceNameCell}>
                  <Text style={styles.serviceNameText}>{service.service}</Text>
                  <Text style={styles.subserviceNameText}>{sub.name}</Text>
                </View>
                <Text style={styles.tableCell}>
                  ${sub.min_price} - ${sub.max_price}
                </Text>
                <Text style={styles.tableCell}>${sub.customer_bid}</Text>
                <Text style={styles.tableCell}>${sub.mechanic_bid}</Text>
              </View>
            )),
          )}
          <View style={styles.totalRow}>
            <Text style={styles.totalCell}>TOTAL</Text>
            <Text style={styles.totalCell} />
            <Text style={styles.totalCell}>
              $
              {appointmentPriceModel?.services?.reduce(
                (acc: number, service: any) =>
                  acc +
                  service.subservice.reduce(
                    (subAcc: number, sub: any) =>
                      subAcc + (parseFloat(sub.customer_bid) || 0),
                    0,
                  ),
                0,
              ) || 0}
            </Text>
            <Text style={styles.totalCell}>
              $
              {appointmentPriceModel?.services?.reduce(
                (acc: number, service: any) =>
                  acc +
                  service.subservice.reduce(
                    (subAcc: number, sub: any) =>
                      subAcc + (parseFloat(sub.mechanic_bid) || 0),
                    0,
                  ),
                0,
              ) || 0}
            </Text>
          </View>
        </View>
        <View style={styles.locationContainer}>
          <View style={styles.locationRow}>
            <Text style={styles.locationText}>
              {appointmentPriceModel?.requestAddress ||
                `${mechLocation.address1}, ${mechLocation.city}, ${mechLocation.country}`}
            </Text>
          </View>
        </View>

        <Text style={styles.sectionTitle}>NOTES AND IMAGES</Text>
        <TextInput
          style={[
            styles.textArea,
            (isCancelLoading || isApproveLoading) && styles.disabledTextArea
          ]}
          multiline
          placeholder="Please add Notes"
          placeholderTextColor={'#5a5757ff'}
          value={notes}
          onChangeText={setNotes}
          editable={!isCancelLoading && !isApproveLoading}
        />
        <TouchableOpacity
          style={[
            styles.imageButton,
            (isCancelLoading || isApproveLoading) && styles.disabledButton
          ]}
          onPress={pickImages}
          disabled={isCancelLoading || isApproveLoading}
        >
          <Text style={[
            styles.imageButtonText,
            (isCancelLoading || isApproveLoading) && styles.disabledText
          ]}>
            Select Images
          </Text>
        </TouchableOpacity>
        <ScrollView
          horizontal
          style={styles.imagePreviewRow}
          showsHorizontalScrollIndicator={false}
          scrollEnabled={!isCancelLoading && !isApproveLoading}
        >
          {uploadfile.map((img, idx) => (
            <Image
              key={idx}
              source={{uri: img.uri}}
              style={styles.previewImage}
            />
          ))}
        </ScrollView>
      </>
    )}

  </ScrollView>

      {/* Buttons in Footer */}
      <View style={[styles.footer, {bottom: insets.bottom, paddingBottom: 20}]}>
        <TouchableOpacity
            style={[styles.cancelButton, isCancelLoading && styles.disabledButton]}
            onPress={cancelAppointment}
            disabled={isCancelLoading}>
            {isCancelLoading ? (
               <Text style={styles.buttonText}>Cancelling...</Text>
            ) : (
                <Text style={styles.buttonText}>Cancel

                 </Text>
            )}
        </TouchableOpacity>

        {hasMechanicBid && mechanicDetail && Object.keys(mechanicDetail).length > 0 && !isAlreadyApproved && (
            <TouchableOpacity
                style={[styles.approveButton, isApproveLoading && styles.disabledButton]}
                onPress={doYouWantToApproveAppointment}
                disabled={isApproveLoading}>
                {isApproveLoading ? (
                     <Text style={styles.buttonText}>Approving... </Text>
                ) : (
                    <Text style={styles.buttonText}>Approve </Text>
                )}
            </TouchableOpacity>
        )}
        {isAlreadyApproved && (
            <View style={[styles.approveButton, styles.alreadyApprovedButton]}>
                <Text style={styles.buttonText}>Already Approved</Text>
            </View>
        )}
      </View>
      <LoaderOverlay visible={!appointmentPriceModel} />
      <LoaderOverlay visible={isApproveLoading} />
      <LoaderOverlay visible={isCancelLoading} />      
      {isProcessingComplete && (
        <View style={styles.interactionBlocker} />
      )}
    </SafeAreaView>
  );
};

export default ConfirmAppointment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    backgroundColor: '#1f2a38',
  },
  navbar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#1f2a38',
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    height: 30,
    width: 120,
  },
  titleBar: {
    backgroundColor: '#a00',
    paddingVertical: 12,
    alignItems: 'center',
  },
  titleBarText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 15,
    paddingBottom: 0,
  },
  bottomSpacer: {
    height: 120, 
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginVertical: 10,
    color: '#fff',
  },
  messageContent: {
    flex: 1,
    flexDirection: 'column',
    backgroundColor: 'transparent',
  },
  mechanicCard: {
    flexDirection: 'row',
    backgroundColor: '#f8f8f8',
    padding: 10,
    borderRadius: 8,
  },
  profileImage: {width: 80, height: 80, borderRadius: 8},
  profileInfo: {marginLeft: 10, flex: 1},
  profileName: {fontSize: 16, fontWeight: 'bold',color: '#555'},
  profileAddress: {fontSize: 14, color: '#555'},
  profileAvailability: {fontSize: 13, color: '#888'},
  profileRating: {fontSize: 13, marginTop: 5,color: '#888'},
  dateTimeContainer: {
    flexDirection: 'row', 
    alignItems: 'center', 
    backgroundColor: '#a00', 
    padding: 12,
    borderRadius: 8,
    marginVertical: 10,
    justifyContent: 'space-between', 
  },
  messageInfo: {
    flex: 1,
    paddingLeft: 12,
    paddingRight: 8,
    paddingVertical: 4,
    justifyContent: 'flex-start',
    backgroundColor: 'transparent',
  },
  dateText: {
    color: '#fff', 
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 14, 
  },
  timeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)', 
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  timeRange:{ fontSize: 14,  marginRight: 8,},
  timeButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  serviceNameCell: {flex: 1},
  serviceNameText: {fontWeight: 'bold', fontSize: 12, color: '#000'},
  subserviceNameText: {fontSize: 12, marginLeft: 10, color: '#000'},
  textArea: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    padding: 10,
    minHeight: 80,
    marginBottom: 10,
    color: '#000',
    backgroundColor: '#f0f0f0',
  },
  imagePreviewRow: {marginVertical: 10},
  previewImage: {width: 70, height: 70, borderRadius: 6, marginRight: 10},
  imageButton: {
    backgroundColor: '#f0f0f0',
    padding: 10,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 10,
  },
  imageButtonText: {
    color: '#000',
    fontSize: 14,
    fontWeight: '500',
  },
  messageMainContent: {
    flexDirection: 'row',
    backgroundColor: 'transparent',
    paddingVertical: 12,
  },
  cancelButton: {
    backgroundColor: '#444',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
    marginRight: 10,
  },
  approveButton: {
    backgroundColor: '#a00',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    backgroundColor: '#1f2a38',
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
    zIndex: 1000, 
    elevation: 10, 
  },
  leftIconBlock: {
    width: 70,
    alignSelf: 'stretch',
    backgroundColor: Colors.PRIMARY_DARK,
    justifyContent: 'center',
    alignItems: 'center',
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
  },
  leftIcon: {
    width: 36,
    height: 36,
    tintColor: Colors.COMMON_WHITE_SHADE,
  },
  buttonText: {color: '#fff', fontWeight: 'bold'},
  tableContainer: {
    backgroundColor: '#f1f1f1',
    borderRadius: 8,
    marginTop: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#ddd',
    paddingVertical: 10,
    paddingHorizontal: 5,
  },
  tableHeaderText: {
    fontWeight: 'bold',
    color: '#333',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 8,
    paddingHorizontal: 5,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  tableCell: {
    flex: 1,
    textAlign: 'center',
    fontSize: 12,
    color: '#333',
  },
  locationContainer: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#f1f1f1',
    borderRadius: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#333',
    flex: 1,
  },
  totalRow: {
    flexDirection: 'row',
    backgroundColor: '#f0f0f0',
    paddingVertical: 10,
    paddingHorizontal: 5,
    borderTopWidth: 2,
    borderTopColor: '#a00',
  },
  totalCell: {
    flex: 1,
    textAlign: 'center',
    fontSize: 14,
    fontWeight: 'bold',
    color: '#a00',
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  // Additional styles for disabled states
  disabledText: {
    color: '#ccc',
  },
  disabledTextArea: {
    backgroundColor: '#f5f5f5',
    borderColor: '#ddd',
    color: '#999',
  },
  // Modern Pending Request Card Styles
  pendingRequestCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 167, 38, 0.3)',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 5,
  },
  pendingHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  pendingIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: 'rgba(255, 167, 38, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  pendingHeaderText: {
    flex: 1,
  },
  pendingTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#333',
    marginBottom: 4,
  },
  pendingSubtitle: {
    fontSize: 14,
    color: '#666',
    fontWeight: '400',
  },
  pendingDateTimeSection: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  pendingDateBox: {
    alignItems: 'center',
    marginRight: 20,
    paddingRight: 20,
    borderRightWidth: 1,
    borderRightColor: '#e0e0e0',
  },
  pendingDateNumber: {
    fontSize: 32,
    fontWeight: '800',
    color: '#FFA726',
    lineHeight: 36,
  },
  pendingDateMonth: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    textTransform: 'uppercase',
    marginTop: 2,
  },
  pendingTimeBox: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  pendingTimeText: {
    fontSize: 15,
    color: '#333',
    fontWeight: '500',
    marginLeft: 8,
  },
  pendingDivider: {
    height: 1,
    backgroundColor: '#e0e0e0',
    marginVertical: 20,
  },
  pendingServicesSection: {
    marginBottom: 20,
  },
  pendingSectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#333',
    marginBottom: 12,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  pendingServiceCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#FFA726',
  },
  pendingServiceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  pendingServiceIconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 167, 38, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  pendingServiceIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFA726',
  },
  pendingServiceName: {
    fontSize: 17,
    fontWeight: '700',
    color: '#333',
    flex: 1,
  },
  pendingSubservicesList: {
    paddingLeft: 12,
  },
  pendingSubserviceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  pendingBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#FFA726',
    marginRight: 10,
  },
  pendingSubserviceText: {
    fontSize: 15,
    color: '#555',
    flex: 1,
    fontWeight: '400',
  },
  pendingStatusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 167, 38, 0.15)',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  pendingStatusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#FFA726',
    marginRight: 8,
  },
  pendingStatusText: {
    fontSize: 15,
    fontWeight: '600',
    color: '#F57C00',
  },
  interactionBlocker: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.7)', 
    zIndex: 9999,
  },
  alreadyApprovedButton: {
    backgroundColor: '#28A745', 
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    flex: 1,
  },
});