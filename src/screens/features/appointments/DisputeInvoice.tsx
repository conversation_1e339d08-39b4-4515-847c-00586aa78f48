import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  Alert,
  Image,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../../utils/configs/types';
import Icon from 'react-native-vector-icons/Ionicons';
import AppBackground from '../../../components/ui/AppBackground';
import { AppCommonIcons, AppStrings } from '../../../utils/constants/AppStrings';
import { VehicleService } from '../../../utils/services/VehicleService';
import { AppointmentService } from '../../../utils/services/AppointmentService';
import { get } from '@react-native-firebase/database';
import {useSafeAreaInsets} from 'react-native-safe-area-context';

const DisputeInvoice: React.FC = () => {
  const navigation = useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const route = useRoute();
  const {'session-details': sessionDetails, mechanicDetail} = route.params as any;
  const insets = useSafeAreaInsets();

  const [disputeArray, setDisputeArray] = useState<any[]>([]);
  const [_extraPop, _setExtraPop] = useState(false);
  const [invoiceData, setInvoiceData] = useState<any>({ services: [], taxes: 0, total: 0 });

  const processInvoiceDetail = useCallback((invoice: any) => {
    let tempInvoice = [];
    for (let sv = 0; sv < invoice.services.length; sv++) {
      let tempSubInvoice = [];
      for (let ssv = 0; ssv < invoice.services[sv].subservice.length; ssv++) {
        if (invoice.services[sv].selected[ssv]) {
          tempSubInvoice.push({
            'name': invoice.services[sv].subservice[ssv],
            'price': invoice.services[sv].mechanicBid[ssv],
          });
        }
      }
      if (tempSubInvoice.length > 0) {
        tempInvoice.push({
          'service': invoice.services[sv].service,
          'subservice': tempSubInvoice,
        });
      }
    }
    const serviceDetails = {
      'services': tempInvoice,
      'taxes': invoice['service-tax'] ? invoice['service-tax'] : 0,
      'total': '',
    };
    calculateTaxes(serviceDetails);
    setInvoiceData(serviceDetails);
  }, []);

  const calculateTaxes = (serviceDetails: any) => {
    serviceDetails.total = 0;
    let totServ = 0;
    for (let sv = 0; sv < serviceDetails.services.length; sv++) {
      for (let ssv = 0; ssv < serviceDetails.services[sv].subservice.length; ssv++) {
        totServ += parseFloat(serviceDetails.services[sv].subservice[ssv].price);
      }
    }
    serviceDetails.serviceFee = totServ.toFixed(2);
    // Calculate total as service fee + percentage of tax
    serviceDetails.total = (totServ + (totServ * (parseFloat(serviceDetails.taxes) / 100))).toFixed(2);
  };

  const fetchInvoiceData = useCallback(async () => {
    try {
      if (!sessionDetails || !sessionDetails['work-request-id']) {
        console.error('Session details or work request ID is missing');
        return;
      }
      const workRequestId = sessionDetails['work-request-id'];
      const invoiceQuery = AppointmentService.fetchInvoiceGenerated(workRequestId);
      const invoiceSnapshot = await get(invoiceQuery);
      if (invoiceSnapshot.exists()) {
        const invoiceData = invoiceSnapshot.val();
        if (invoiceData && invoiceData[workRequestId]) {
          const invoiceDetail = invoiceData[workRequestId];
          processInvoiceDetail(invoiceDetail);
        } else {
          console.error('Invoice data not found for work request ID:', workRequestId);
        }
      } else {
        console.error('Invoice snapshot does not exist');
      }
    } catch (error) {
      console.error('Error fetching invoice:', error);
    }
  }, [sessionDetails, processInvoiceDetail]);

  useEffect(() => {
    fetchDisputeQuestions();
    fetchInvoiceData();
  }, [fetchInvoiceData]);

  const fetchDisputeQuestions = async () => {
    const questions = await VehicleService.getDisputeQuestions();
    setDisputeArray(questions);
    console.log('disputeArray : ', questions);
  };

  const toggleCheck = (index: number) => {
    const newArray = [...disputeArray];
    newArray[index].isChecked = !newArray[index].isChecked;
    setDisputeArray(newArray);
  };

  const onInputChange = (index: number, text: string) => {
    const newArray = [...disputeArray];
    newArray[index].input = text;
    setDisputeArray(newArray);
  };

  const checkFormValid = () => {
    let isAnyChecked = false;
    for (let i = 0; i < disputeArray.length; i++) {
      if (disputeArray[i].isChecked) {
        isAnyChecked = true;
        if (
          disputeArray[i].type === 'entry' &&
          disputeArray[i].input.trim() === ''
        ) {
          return false;
        }
      }
    }
    return isAnyChecked;
  };

  const popWarningAlert = (message: string) => {
    Alert.alert('Warning', message, [{text: 'OK'}]);
  };

  const prepareDisputeResponse = async () => {
    let preparedDispute: any[] = [];
    disputeArray.forEach(element => {
      if (element.isChecked) {
        if (element.type === 'entry') {
          preparedDispute.push({
            selected: element.question,
            comment: element.input,
          });
        } else {
          preparedDispute.push({
            selected: element.question,
          });
        }
      }
    });
    console.log('preparedDispute : ', preparedDispute);
    await VehicleService.updateDisputeToWorkRequest(
      sessionDetails['work-request-id'],
      preparedDispute,
    );
    await VehicleService.updateLog(sessionDetails['work-request-id']).child('invoice').child('disputed-time').set(Math.floor(Date.now() / 1000));
  };

  const handleCheckout = async () => {
    await prepareDisputeResponse();
    navigation.navigate('CheckOutScreen', {
      mechanicDetail,
      sessionDetail: { details: sessionDetails.details },
      costDetail: invoiceData,
      onPayNow: () => {
        console.log('Pay Now clicked');
      },
      onRedeemPoints: () => {
        console.log('Redeem Points clicked');
      },
    });
  };

  const checkOut = () => {
    console.log('Filled book appointment form');
    if (checkFormValid()) {
      console.log('form get valid');
      Alert.alert(
        'Notice',
        'myCANx response team is available 24x7. Will contact you as soon as possible after you pay the service.',
        [
          {
            text: 'OK',
            onPress: handleCheckout,
          },
        ],
      );
    } else {
      if (!_extraPop) {
        popWarningAlert('Choose atleast one option to proceed with checkout');
      }
      console.log('form not valid');
    }
  };

  return (
    <View style={[styles.container, {paddingTop: insets.top}]}>
       <AppBackground />
             <View style={styles.header}>
                <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
                    <Icon name="arrow-back" size={24} color="#fff" />
                </TouchableOpacity>
                <Image
                    source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
                    style={styles.logo}
                    resizeMode="contain"
                />
            </View>
            <View style={styles.navTitle}>
                <Text style={styles.navTitleText}>{AppStrings.MCX_DISPUTE_INVOICE_TITLE} </Text>
            </View>
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.sectionTitle}>DISPUTE QUESTIONS</Text>
        {disputeArray.map((item, index) => (
          <View key={index} style={styles.questionContainer}>
            <TouchableOpacity
              style={styles.checkbox}
              onPress={() => toggleCheck(index)}>
              <View
                style={[
                  styles.checkboxBox,
                  item.isChecked && styles.checkboxChecked,
                ]}>
                {item.isChecked && (
                  <Icon name="checkmark" size={16} color="#fff" />
                )}
              </View>
            </TouchableOpacity>
            <View style={styles.questionTextContainer}>
              <Text style={styles.questionText}>{item.question}</Text>
              {item.type === 'entry' && item.isChecked && (
                <TextInput
                  style={styles.textInput}
                  placeholder="Enter your comment"
                  value={item.input}
                  onChangeText={text => onInputChange(index, text)}
                  multiline
                />
              )}
            </View>
          </View>
        ))}
      </ScrollView>
      <TouchableOpacity style={[styles.checkoutButton, {bottom: insets.bottom}]} onPress={checkOut}>
        <Text style={styles.checkoutButtonText}>CHECK OUT</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1f2a38',
  },
  header: {
    height: 56,
    backgroundColor: '#1f2a38',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
  },
  backButton: {
        position: 'absolute',
        left: 15,
        padding: 8,
        zIndex: 10,
    },
    logo: {
        width: 120,
        height: 40,
        resizeMode: 'contain',
        tintColor: '#fff',
    },
    navTitle: {
        backgroundColor: '#a00',
        paddingVertical: 10,
        alignItems: 'center',
    },
    navTitleText: {
        color: '#fff',
        fontWeight: 'bold',
        fontSize: 18,
    },
  backButtonText: {
    color: '#fff',
    fontSize: 16,
  },
  headerTitle: {
    flex: 1,
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
    textAlign: 'center',
    marginRight: 40,
  },
  content: {
    padding: 15,
  },
  sectionTitle: {
    color: '#ccc',
    fontWeight: 'bold',
    fontSize: 14,
    marginBottom: 10,
  },
  questionContainer: {
    flexDirection: 'row',
    marginBottom: 15,
    backgroundColor: '#222',
    padding: 10,
    borderRadius: 8,
  },
  checkbox: {
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  checkboxBox: {
    width: 20,
    height: 20,
    borderWidth: 2,
    borderColor: '#ccc',
    borderRadius: 3,
  },
  checkboxChecked: {
    backgroundColor: '#b22222',
    borderColor: '#b22222',
  },
  questionTextContainer: {
    flex: 1,
  },
  questionText: {
    color: '#fff',
    fontSize: 14,
  },
  textInput: {
    marginTop: 8,
    backgroundColor: '#333',
    color: '#fff',
    borderRadius: 5,
    padding: 8,
    minHeight: 40,
    textAlignVertical: 'top',
  },
  checkoutButton: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#b22222',
    padding: 16,
    alignItems: 'center',
  },
  checkoutButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default DisputeInvoice;
