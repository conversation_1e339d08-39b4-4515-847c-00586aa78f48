import React, {useEffect, useState, useCallback} from 'react';
import {
  View,
  Text,
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  ActivityIndicator
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {get, ref, getDatabase} from '@react-native-firebase/database';
import AppBackground from '../../../components/ui/AppBackground';
import {AppCommonIcons, AppStrings} from '../../../utils/constants/AppStrings';
import Icon from 'react-native-vector-icons/Ionicons';
import {AppointmentService} from '../../../utils/services/AppointmentService';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import NetInfo from '@react-native-community/netinfo';
const AppointmentSession: React.FC = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const {'session-details': sessionDetails, mechanicDetail} =
    route.params as any;
  const insets = useSafeAreaInsets();
  const [invoiceData, setInvoiceData] = useState<any>({
    services: [],
    taxes: 0,
    total: 0,
  });
  const [loadingInvoice, setLoadingInvoice] = useState(true);
  interface SessionDataType {
    location: string[];
    duration: string;
    startTime: number | null;
    endTime: number | null;
    startTimeFormatted?: string;
    endTimeFormatted?: string;
  }
  const [sessionData, setSessionData] = useState<SessionDataType | null>(null);
  const [_isSessionCancelled, _setIsSessionCancelled] =
    useState<boolean>(false);
  const [timerDisplay, setTimerDisplay] = useState<string>('');
  const [timerInterval, setTimerInterval] = useState<NodeJS.Timeout | null>(
    null,
  );

  const processInvoiceDetail = useCallback((invoice: any) => {
    if (!invoice || typeof invoice !== 'object') {
      console.warn('Invalid invoice data: invoice is not an object');
      return;
    }
    if (!invoice.services || !Array.isArray(invoice.services)) {
      console.warn('Invalid invoice data: missing or invalid services array');
      return;
    }
    let tempInvoice = [];
    for (let sv = 0; sv < invoice.services.length; sv++) {
      const service = invoice.services[sv];
      if (!service || typeof service !== 'object') continue;
      if (!service.subservice || !Array.isArray(service.subservice)) continue;
      if (!service.selected || !Array.isArray(service.selected)) continue;
      if (!service.mechanicBid || !Array.isArray(service.mechanicBid)) continue;
      let tempSubInvoice = [];
      for (let ssv = 0; ssv < service.subservice.length; ssv++) {
        if (
          service.selected[ssv] &&
          service.subservice[ssv] &&
          service.mechanicBid[ssv]
        ) {
          tempSubInvoice.push({
            name: service.subservice[ssv],
            price: service.mechanicBid[ssv],
          });
        }
      }
      if (tempSubInvoice.length > 0) {
        tempInvoice.push({
          service: service.service || '',
          subservice: tempSubInvoice,
        });
      }
    }
    const serviceDetails = {
      services: tempInvoice,
      taxes:
        invoice['service-tax'] && typeof invoice['service-tax'] === 'number'
          ? invoice['service-tax']
          : 0,
      total: '',
    };
    calculateTaxes(serviceDetails);
    setInvoiceData(serviceDetails);
  }, []);

  const calculateTaxes = (serviceDetails: any) => {
    serviceDetails.total = 0;
    let totServ = 0;
    for (let sv = 0; sv < serviceDetails.services.length; sv++) {
      for (
        let ssv = 0;
        ssv < serviceDetails.services[sv].subservice.length;
        ssv++
      ) {
        const price = parseFloat(
          serviceDetails.services[sv].subservice[ssv].price,
        );
        if (!isNaN(price)) {
          totServ += price;
        }
      }
    }
    serviceDetails.serviceFee = totServ.toFixed(2);
    const taxes = parseFloat(serviceDetails.taxes);
    if (!isNaN(taxes)) {
      serviceDetails.total = (totServ + (taxes * totServ) / 100).toFixed(2);
    } else {
      serviceDetails.total = totServ.toFixed(2);
    }
  };

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      await fetchSessionAndWorkRequest();
    };

    const fetchSessionAndWorkRequest = async () => {
      setLoadingInvoice(true);
      try {
        const workRequestId = sessionDetails['work-request-id'];
        if (!workRequestId) {
          console.warn('No work-request-id found in sessionDetails');
          return;
        }
        const workRequestQuery =
          AppointmentService.fetchWorkRequest(workRequestId);
        const workRequestSnapshot = await get(workRequestQuery);

        if (!workRequestSnapshot.exists()) {
          console.warn('Work-request not found for id:', workRequestId);
          return;
        }
        const workRequestData = workRequestSnapshot.val()[workRequestId];
        const invoiceQuery =
          AppointmentService.fetchInvoiceGenerated(workRequestId);
        const invoiceSnapshot = await get(invoiceQuery);

        if (invoiceSnapshot.exists()) {
          const invoiceDetail = invoiceSnapshot.val()[workRequestId];
          processInvoiceDetail(invoiceDetail);
        }
        const sessionId = workRequestData.sessionId;
        const appointmentId = workRequestData['appointment-id'];
        if (!sessionId) {
          console.warn('SessionId missing in work-request data');
          return;
        }
        const sessionQuery = AppointmentService.fetchSessionDetail(sessionId);
        const sessionSnapshot = await get(sessionQuery);

        if (!sessionSnapshot.exists()) {
          console.warn('Session not found for id:', sessionId);
          return;
        }
        if (appointmentId) {
          const db = getDatabase();
          const cancelStateRef = ref(
            db,
            `appointment/${appointmentId}/cancelled-state`,
          );
          const cancelSnapshot = await get(cancelStateRef);
          if (cancelSnapshot.exists()) {
            _setIsSessionCancelled(true);
            navigation.goBack();
            return;
          }
        }
        const combinedSessionData: SessionDataType = {
          location:
            workRequestData['request-address'] &&
            workRequestData['request-address'].address_array
              ? [
                  workRequestData['request-address'].address_array.address1 ||
                    '',
                  workRequestData['request-address'].address_array.address2 ||
                    '',
                  workRequestData['request-address'].address_array.city
                    ? workRequestData['request-address'].address_array.city +
                      ', ' +
                      workRequestData['request-address'].address_array.state
                    : '',
                  workRequestData['request-address'].address_array.country &&
                  workRequestData['request-address'].address_array.zipcode
                    ? workRequestData['request-address'].address_array.country +
                      ' - ' +
                      workRequestData['request-address'].address_array.zipcode
                    : workRequestData['request-address'].address_array
                        .country || '',
                ].filter(
                  line => line && line !== 'undefined' && line.trim() !== '',
                )
              : ['Not available'],
          duration: '',
          startTime: workRequestData['created-time'],
          endTime: workRequestData['completed-time'] || null,
          startTimeFormatted: '',
          endTimeFormatted: '',
        };
        const sessionVal = sessionSnapshot.exists()
          ? sessionSnapshot.val()[sessionId]
          : null;
        if (sessionVal) {
          combinedSessionData.duration = sessionVal.duration || '';
          combinedSessionData.startTime =
            sessionVal['created-time'] || combinedSessionData.startTime;
          combinedSessionData.endTime =
            sessionVal['completed-time'] || combinedSessionData.endTime;
        }
        const formatTimeOnly = (timestamp: number | null) => {
          if (!timestamp) return '';
          const date = new Date(timestamp * 1000);
          return date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
            hour12: true,
          });
        };
        if (combinedSessionData.startTime && combinedSessionData.endTime) {
          const diffSeconds =
            combinedSessionData.endTime - combinedSessionData.startTime;
          const hours = Math.floor(diffSeconds / 3600);
          const minutes = Math.floor((diffSeconds % 3600) / 60);
          if (hours > 0) {
            combinedSessionData.duration = `${hours} hour${
              hours > 1 ? 's' : ''
            } ${minutes} minute${minutes !== 1 ? 's' : ''}`;
          } else {
            combinedSessionData.duration = `${minutes} minute${
              minutes !== 1 ? 's' : ''
            }`;
          }
        }

        combinedSessionData.startTimeFormatted = formatTimeOnly(
          combinedSessionData.startTime,
        );
        combinedSessionData.endTimeFormatted = formatTimeOnly(
          combinedSessionData.endTime,
        );

        setSessionData(combinedSessionData as SessionDataType);
        if (!combinedSessionData.endTime && combinedSessionData.startTime) {
          startTimer(combinedSessionData.startTime);
        }
      } catch (error) {
        console.error('Error fetching session/work-request:', error);
      } finally {
        setLoadingInvoice(false);
      }
    };

    checkNetworkAndLoad();
  }, [sessionDetails, navigation, processInvoiceDetail]);

  const startTimer = (startTime: number) => {
    const updateTimer = () => {
      const now = Date.now() / 1000;
      const elapsed = now - startTime;
      const hours = Math.floor(elapsed / 3600);
      const minutes = Math.floor((elapsed % 3600) / 60);
      const seconds = Math.floor(elapsed % 60);

      let display = '';
      display = `${hours.toString().padStart(2, '0')}:${minutes
        .toString()
        .padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      setTimerDisplay(display);
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);
    setTimerInterval(interval);
  };

  useEffect(() => {
    return () => {
      if (timerInterval) {
        clearInterval(timerInterval);
      }
    };
  }, [timerInterval]);

  const onDisputeInvoice = () => {
    (navigation as any).navigate('DisputeInvoice', {
      'session-details': sessionDetails,
      mechanicDetail: mechanicDetail,
    });
  };

  const onCheckOut = () => {
    (navigation as any).navigate('CheckOutScreen', {
      mechanicDetail,
      sessionDetail: {details: sessionDetails.details, ...sessionData},
      costDetail: invoiceData,
      workRequestId: sessionDetails['work-request-id'],
      onPayNow: () => {
        console.log('Pay Now clicked');
      },
      onRedeemPoints: () => {
        console.log('Redeem Points clicked');
      },
    });
  };

  const services = sessionDetails?.details?.services || [];
  return (
    <SafeAreaView
      style={[
        styles.container,
        {paddingTop: insets.top},
        {paddingBottom: 0},
      ]}>
      <AppBackground />
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Image
          source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
      <View style={styles.navTitle}>
        <Text style={styles.navTitleText}>
          {AppStrings.MCX_APPOINTMENT_SESSION_TITLE}
        </Text>
      </View>
      {/* Scrollable Content */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={[
          styles.content,
          {paddingBottom: sessionData?.endTime ? 120 : insets.bottom + 20},
        ]}
        showsVerticalScrollIndicator={false}>
        <Text style={styles.sectionTitle}>
          {AppStrings.MCX_MY_MECHANIC_TITLE}
        </Text>
        <View style={styles.card}>
          <Image
            source={{uri: mechanicDetail?.imageUrl}}
            style={styles.avatar}
          />
          <View style={{flex: 1}}>
            <Text style={styles.mechanicName}>
              {mechanicDetail?.['first-name']} {mechanicDetail?.['last-name']}
            </Text>
            <Text style={styles.mechanicAddress}>
              {mechanicDetail?.address1}, {mechanicDetail?.address2}
            </Text>
            <View style={styles.availabilityRow}>
              <Text style={styles.availabilityLabel}>Availability</Text>
              <Text style={styles.availabilityValue}>
                {mechanicDetail?.availability ? 'Open' : 'Appointment'}
              </Text>
            </View>
            <Text style={styles.rating}>
              ⭐ {mechanicDetail?.['mechanic-rating'] || 0}
            </Text>
          </View>
        </View>
        <View style={styles.appointmentCard}>
          <View style={[styles.dateBlock, { alignSelf: 'stretch', height: 'auto', paddingVertical: 20 }]}>
            <Text style={styles.dateNumber}>
              {sessionDetails?.details?.day}
            </Text>
            <Text style={styles.dateMonth}>
              {sessionDetails?.details?.month}
            </Text>
          </View>
          <View style={styles.serviceInfo}>
            {services.map((service: any, index: number) => (
              <View key={index} style={{ marginBottom: 8 }}>
                <Text style={styles.serviceTitle}>{service.service}</Text>
                {service.subservice && service.subservice.map((sub: string, subIndex: number) => (
                  <Text key={subIndex} style={[styles.serviceDescription, { marginLeft: 16, fontSize: 12 }]}>{sub}</Text>
                ))}
              </View>
            ))}
            <Text style={styles.serviceTime}>
              {sessionDetails?.details?.time}
            </Text>
          </View>
        </View>
         {sessionData?.endTime && !_isSessionCancelled && (
           <>
             <Text style={styles.sectionTitle}>INVOICE DETAILS</Text>
             <View style={styles.invoiceBox}>
               {loadingInvoice ? (
                 <View style={styles.loadingContainer}>
                   <ActivityIndicator size="large" color="#900" />
                   <Text style={styles.loadingText}>Loading invoice details...</Text>
                 </View>
               ) : (
                 <>
                   <View style={[styles.invoiceRow, styles.invoiceHeaderRow]}>
                     <Text
                       style={[styles.invoiceLabel, styles.invoiceHeaderText]}>
                       Service name
                     </Text>
                     <Text
                       style={[styles.invoiceLabel, styles.invoiceHeaderText]}>
                       Rate
                     </Text>
                   </View>
                   {invoiceData.services && invoiceData.services.length > 0 ? (
                     invoiceData.services.map((service: any, idx: number) => (
                       <View key={idx}>
                         <View style={styles.invoiceRow}>
                           <Text style={styles.invoiceLabel}>
                             {service.service}
                           </Text>
                           <Text />
                         </View>
                         {service.subservice.map((sub: any, subIdx: number) => (
                           <View key={subIdx} style={styles.invoiceRow}>
                             <Text style={styles.invoiceLabel}> - {sub.name}</Text>
                             <Text style={{color: '#000'}}>${sub.price}</Text>
                           </View>
                         ))}
                       </View>
                     ))
                   ) : (
                     <Text style={styles.invoiceLabel}>
                       No invoice data available
                     </Text>
                   )}
                   {invoiceData.services && invoiceData.services.length > 0 && (
                     <>
                       <View style={styles.invoiceRow}>
                         <Text style={styles.invoiceTaxLabel}>Taxes</Text>
                         <Text style={{color: '#000'}}>{invoiceData.taxes}%</Text>
                       </View>
                       <View style={styles.invoiceRow}>
                         <Text style={[styles.totalText, styles.invoiceTaxLabel]}>
                           Total
                         </Text>
                         <Text style={styles.totalText}>${invoiceData.total}</Text>
                       </View>
                     </>
                   )}
                 </>
               )}
             </View>
           </>
         )}
        {/* {sessionData?.endTime && !_isSessionCancelled && (
          <>
           
            
              {loadingInvoice ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator size="large" color="#900" />
                  <Text style={styles.loadingText}>Loading invoice details...</Text>
                </View>
              ) : (
                <>
                  <View style={[styles.invoiceRow, styles.invoiceHeaderRow]}>
                    <Text
                      style={[styles.invoiceLabel, styles.invoiceHeaderText]}>
                      Service name
                    </Text>
                    <Text
                      style={[styles.invoiceLabel, styles.invoiceHeaderText]}>
                      Rate
                    </Text>
                  </View>
                  {invoiceData.services && invoiceData.services.length > 0 ? (
                    invoiceData.services.map((service: any, idx: number) => (
                      <View key={idx}>
                        <View style={styles.invoiceRow}>
                          <Text style={styles.invoiceLabel}>
                            {service.service}
                          </Text>
                          <Text />
                        </View>
                        {service.subservice.map((sub: any, subIdx: number) => (
                          <View key={subIdx} style={styles.invoiceRow}>
                            <Text style={styles.invoiceLabel}> - {sub.name}</Text>
                            <Text style={{color: '#000'}}>${sub.price}</Text>
                          </View>
                        ))}
                      </View>
                    ))
                  ) : (
                    <Text style={styles.invoiceLabel}>
                      No invoice data available
                    </Text>
                  )}
                  {invoiceData.services && invoiceData.services.length > 0 && (
                    <>
                      <View style={styles.invoiceRow}>
                        <Text style={styles.invoiceTaxLabel}>Taxes</Text>
                        <Text style={{color: '#000'}}>{invoiceData.taxes}%</Text>
                      </View>
                      <View style={styles.invoiceRow}>
                        <Text style={[styles.totalText, styles.invoiceTaxLabel]}>
                          Total
                        </Text>
                        <Text style={styles.totalText}>${invoiceData.total}</Text>
                      </View>
                    </>
                  )}
                </>
              )}
           
          </>
        )} */}
        <Text style={styles.sectionTitle}>SESSION DETAILS</Text>
        <View style={styles.sessionBox}>
          <View style={{flex: 1}}>
            <Text style={styles.sessionLabel}>LOCATION</Text>
            {sessionData?.location?.map((line: string, idx: number) => (
              <Text key={idx} style={styles.sessionValue}>
                {line}
              </Text>
            )) || (
              <Text style={styles.sessionValue}>Location not available</Text>
            )}
          </View>
          <View style={{flex: 1, marginLeft: 10}}>
            <Text style={styles.sessionLabel}>WORK SESSION TIME</Text>
            {sessionData?.endTime ? (
              <Text style={styles.sessionValue}>
                {sessionData?.duration || ''}
              </Text>
            ) : (
              <Text style={styles.sessionValue}>{timerDisplay}</Text>
            )}
            <Text style={[styles.sessionValue, {marginTop: 8}]}>
              Start Time {sessionData?.startTimeFormatted || ''}
            </Text>
            {sessionData?.endTimeFormatted ? (
              <Text style={[styles.sessionValue, {marginTop: 4}]}>
                End Time {sessionData.endTimeFormatted}
              </Text>
            ) : null}
          </View>
        </View>
      </ScrollView>

      {/* Fixed Footer */}
      {sessionData?.endTime && !_isSessionCancelled && (
        <View style={[styles.footer, {paddingBottom: insets.bottom}]}>
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={styles.disputeButton}
              onPress={onDisputeInvoice}>
              <Text style={styles.buttonText}>Dispute Invoice</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.checkoutButton}
              onPress={onCheckOut}>
              <Text style={styles.buttonText}>CHECK OUT</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </SafeAreaView>
  );
};

export default AppointmentSession;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //backgroundColor: 'transparent',
  },
  headerContainer: {
    backgroundColor: '#1f2a38',
    zIndex: 2,
  },
  header: {
    height: 56,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
  },
  backButton: {
    position: 'absolute',
    left: 15,
    padding: 8,
    zIndex: 10,
  },
  logo: {
    width: 120,
    height: 40,
    resizeMode: 'contain',
    tintColor: '#fff',
  },
  navTitle: {
    backgroundColor: '#a00',
    paddingVertical: 12,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  navTitleText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 15,
  },
  sectionTitle: {
    color: '#fff',
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 8,
    fontSize: 14,
  },
  card: {
    flexDirection: 'row',
    backgroundColor: '#222',
    padding: 12,
    borderRadius: 8,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 12,
  },
  mechanicName: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  mechanicAddress: {
    color: '#ccc',
    fontSize: 13,
  },
  availabilityRow: {
    flexDirection: 'row',
    marginTop: 4,
    alignItems: 'center',
  },
  availabilityLabel: {
    color: '#ccc',
    fontSize: 13,
    marginRight: 6,
    fontWeight: 'bold',
  },
  availabilityValue: {
    color: '#b22222',
    fontSize: 13,
    fontWeight: 'bold',
  },
  rating: {
    color: '#f4a261',
    marginTop: 4,
    fontSize: 14,
  },
  appointmentCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 10,
    borderRadius: 8,
    marginVertical: 10,
    alignItems: 'flex-start',
  },
  dateBlock: {
    backgroundColor: '#b22222',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
    marginRight: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
  },
  dateNumber: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  dateMonth: {
    color: '#fff',
    fontSize: 14,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceTitle: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 16,
  },
  serviceDescription: {
    color: '#b22222',
    fontSize: 14,
    marginTop: 2,
  },
  serviceTime: {
    color: '#555',
    fontSize: 12,
    marginTop: 4,
  },
  invoiceBox: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 12,
    marginBottom: 15,
  },
  invoiceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 4,
  },
  invoiceHeaderRow: {
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
    paddingBottom: 6,
    marginBottom: 6,
  },
  invoiceLabel: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#000',
  },
  invoiceHeaderText: {
    color: '#b22222',
  },
  invoiceDescription: {
    color: '#444',
    marginBottom: 8,
    fontSize: 13,
  },
  invoiceTaxLabel: {
    color: '#b22222',
    fontWeight: 'bold',
    fontSize: 14,
  },
  totalText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 16,
  },
  sessionBox: {
    flexDirection: 'row',
    backgroundColor: '#222',
    padding: 12,
    borderRadius: 8,
  },
  sessionLabel: {
    color: '#ccc',
    fontWeight: 'bold',
    marginBottom: 4,
    fontSize: 13,
  },
  sessionValue: {
    color: '#ccc',
    fontSize: 13,
  },
  disputeButton: {
    flex: 1,
    backgroundColor: '#b22222',
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginRight: 8,
  },
  checkoutButton: {
    flex: 1,
    backgroundColor: '#222',
    padding: 14,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#09090aff',
    borderTopWidth: 1,
    borderTopColor: '#333',
    zIndex: 2,
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  timerContainer: {
    backgroundColor: '#b22222',
    padding: 14,
    borderRadius: 8,
    marginTop: 15,
    alignItems: 'center',
  },
  timerLabel: {
    color: '#fff',
    fontSize: 14,
    marginBottom: 5,
  },
  timerDisplay: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  buttonRow: {
    flexDirection: 'row',
    paddingHorizontal: 15,
    paddingVertical: 10,
  },
  loadingContainer: {
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
});
