import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {get} from '@react-native-firebase/database';
import {VehicleService} from '../../../utils/services/VehicleService';
import ProfileCard from '../../../components/common/ProfileCard';
import ServicesMessage from '../../../components/common/ServiceMessage';
import AppBackground from '../../../components/ui/AppBackground';
import AppBar from '../../../components/common/AppBar';
import {THEME} from '../../../utils/constants/Theme';

// Helper function to format duration in milliseconds to HH:MM:SS format
const prepareDuration = (milliseconds: number): string => {
  if (!milliseconds) return '--';
  
  const totalSeconds = Math.floor(milliseconds / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;
  
  const parts = [];
  if (hours > 0) parts.push(`${hours}h`);
  if (minutes > 0) parts.push(`${minutes}m`);
  if (seconds > 0 && hours === 0) parts.push(`${seconds}s`);
  
  return parts.join(' ') || '--';
};

type CompletedSessionRouteParams = {
  'appointment-detail': any;
};

const CompletedSession = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const [loading, setLoading] = useState(true);
  const [mechanicDetail, setMechanicDetail] = useState<any>(null);
  const [invoiceDetails, setInvoiceDetails] = useState<any>(null);

  // @ts-ignore - TypeScript doesn't know about our route params
  const {'appointment-detail': appointment} = route.params as CompletedSessionRouteParams;

  useEffect(() => {
    const loadData = async () => {
      try {
        // Load mechanic details
        if (appointment.details.mechanic) {
          const mechanicSnapshot = await get(
            VehicleService.getMechanicDetail(appointment.details.mechanic),
          );
          setMechanicDetail(mechanicSnapshot.val());
        }

        // Load invoice details
        const invoiceSnapshot = await get(
          VehicleService.fetchInvoiceGenerated(appointment['work-request-id']),
        );
        const invoiceData = invoiceSnapshot.val()[appointment['work-request-id']];

        if (invoiceData) {
          const workReqSnapshot = await get(
            VehicleService.fetchWorkRequest(appointment['work-request-id']),
          );
          const workReqDetail = workReqSnapshot.val();

          // Process invoice details according to Ionic implementation
          const tempInvoice = [];
             console.log('invoiceData',invoiceData)
          if (invoiceData.services) {
            for (let sv = 0; sv < invoiceData.services.length; sv++) {
              console.log('invoiceData',invoiceData)
              const tempSubInvoice = [];
              if (invoiceData.services[sv].subservice) {
                for (let ssv = 0; ssv < invoiceData.services[sv].subservice.length; ssv++) {
                  if (invoiceData.services[sv].selected && invoiceData.services[sv].selected[ssv]) {
                    tempSubInvoice.push({
                      name: invoiceData.services[sv].subservice[ssv],
                      price: invoiceData.services[sv].mechanicBid?.[ssv] || 0,
                    });
                  }
                }
              }
              
              if (tempSubInvoice.length > 0) {
                tempInvoice.push({
                  service: invoiceData.services[sv].service,
                  subservice: tempSubInvoice,
                });
              }
            }
          }

          const processedInvoice = {
            services: tempInvoice.length > 0 ? tempInvoice : appointment.details.services,
            confirmationId: invoiceData.confirmationId || 'N/A',
            costDetail: {
              serviceFee: invoiceData.costDetails?.serviceFee || 0,
              taxes: invoiceData.costDetails?.taxes || 0,
              subTotal: invoiceData.costDetails?.subTotal || 0,
              tip: invoiceData.costDetails?.tip || 0,
              couponFee: invoiceData.costDetails?.couponFee || 0,
              total: invoiceData.costDetails?.total || 0,
            },
            paymentType: invoiceData.paymentType,
            duration: invoiceData.sessionTime ? prepareDuration(invoiceData.sessionTime) : '--',
            location: workReqDetail?.[appointment['work-request-id']]?.['request-address'] || 'Location not available',
            'visit-type': 'Returning Visit',
          };

          setInvoiceDetails(processedInvoice);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [appointment]);

  if (loading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <SafeAreaView style={[styles.container, {paddingTop: insets.top}]}>
      <AppBackground />
      <AppBar
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />
      <View style={styles.headerContainer}>
        <Text style={styles.headerTitle}>COMPLETED SESSION</Text>
      </View>
      <ScrollView style={styles.scrollView}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>MY MECHANIC</Text>
        {mechanicDetail && (
          <ProfileCard
            imageUrl={mechanicDetail.imageUrl}
            name={`${mechanicDetail['first-name'] || ''} ${
              mechanicDetail['last-name'] || ''
            }`}
            address={mechanicDetail.address2}
            availabilityStatus={
              mechanicDetail.availability ? 'Available' : 'Not Available'
            }
            rate={mechanicDetail['mechanic-rating']}
          />
        )}

        <View style={styles.appointmentDetails}>
          <ServicesMessage
            isIcon={true}
            day={appointment.details.day}
            month={appointment.details.month}
            services={appointment.details.services}
            time={appointment.details.time}
          />
        </View>
      </View>

      {invoiceDetails && (
        <>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>SESSION DETAILS</Text>
            
            {/* Services and Subservices */}
            {invoiceDetails.services?.map((sessionService: any, index: number) => (
              <View key={`service-${index}`} style={styles.serviceSection}>
                <View style={styles.serviceHeader}>
                  <Text style={styles.serviceTitle}>{sessionService.service}</Text>
                </View>
                
                {sessionService.subservice?.map((subServe: any, subIndex: number) => (
                  <View key={`subservice-${index}-${subIndex}`} style={styles.subserviceRow}>
                    <View style={styles.subserviceNameContainer}>
                      <Text style={styles.subserviceName}>{subServe.name}</Text>
                    </View>
                    <View style={styles.priceContainer}>
                      <Text style={styles.priceText}>${subServe.price}</Text>
                    </View>
                  </View>
                ))}
              </View>
            ))}

            {/* Duration */}
            <View style={styles.detailItem}>
              <View style={styles.detailLabelContainer}>
                <Text style={styles.detailLabel}>Duration</Text>
              </View>
              <View style={styles.detailValueContainer}>
                <Text style={styles.detailValue}>{invoiceDetails.duration || '---'}</Text>
              </View>
            </View>
            <View style={styles.divider} />

            {/* Visit Type */}
            {invoiceDetails['visit-type'] && (
              <>
                <View style={styles.detailItem}>
                  <View style={styles.detailLabelContainer}>
                    <Text style={styles.detailLabel}>Visit Type</Text>
                  </View>
                  <View style={styles.detailValueContainer}>
                    <Text style={styles.detailValue}>{invoiceDetails['visit-type']}</Text>
                  </View>
                </View>
                <View style={styles.divider} />
              </>
            )}

            {/* Location */}
            {invoiceDetails.location && (
              <View style={styles.detailItem}>
                <View style={styles.detailLabelContainer}>
                  <Text style={styles.detailLabel}>Location</Text>
                </View>
                <View style={styles.detailValueContainer}>
                  <Text style={[styles.detailValue, styles.addressText]}>
                    {typeof invoiceDetails.location === 'string' 
                      ? invoiceDetails.location 
                      : invoiceDetails.location.formatted_address || 'Location not available'}
                  </Text>
                </View>
              </View>
            )}
            {invoiceDetails.location && <View style={styles.divider} />}
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>COST DETAILS</Text>
            <View style={styles.costItem}>
              <Text>Service Fee</Text>
              <Text>${invoiceDetails.costDetail.serviceFee}</Text>
            </View>
            <View style={styles.divider} />

            <View style={styles.costItem}>
              <Text>Taxes</Text>
              <Text>{invoiceDetails.costDetail.taxes}%</Text>
            </View>
            <View style={styles.divider} />

            <View style={[styles.costItem, styles.subTotal]}>
              <Text>Sub Total</Text>
              <Text>${invoiceDetails.costDetail.subTotal}</Text>
            </View>
            <View style={styles.divider} />

            <View style={styles.costItem}>
              <Text>Confirmation ID</Text>
              <Text>{invoiceDetails.confirmationId}</Text>
            </View>
            <View style={styles.divider} />

            <View style={styles.costItem}>
              <Text>Payment Type</Text>
              <Text>{invoiceDetails.paymentType}</Text>
            </View>
            <View style={styles.divider} />

            <View style={styles.costItem}>
              <Text>Tip</Text>
              <Text>${invoiceDetails.costDetail.tip}</Text>
            </View>
            <View style={styles.divider} />

            {invoiceDetails.costDetail.couponFee > 0 && (
              <>
                <View style={styles.costItem}>
                  <Text>Coupon Discount</Text>
                  <Text>${invoiceDetails.costDetail.couponFee}</Text>
                </View>
                <View style={styles.divider} />
              </>
            )}

            <View style={[styles.costItem, styles.total]}>
              <Text>Total</Text>
              <Text>${invoiceDetails.costDetail.total}</Text>
            </View>
          </View>
        </>
      )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: THEME.COLORS.BACKGROUND,
  },
  headerContainer: {
    backgroundColor: THEME.COLORS.PRIMARY_RED,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 16,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  appointmentDetails: {
    marginTop: 16,
  },
  // Service section styles
  serviceSection: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
  },
  serviceHeader: {
    marginBottom: 8,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  serviceTitle: {
    fontSize: 15,
    fontWeight: '600',
    color: '#000',
  },
  subserviceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
    paddingLeft: 8,
  },
  subserviceNameContainer: {
    flex: 2,
  },
  subserviceName: {
    fontSize: 14,
    color: '#666',
  },
  priceContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  priceText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
  },
  // Detail item styles
  detailItem: {
    flexDirection: 'row',
    paddingVertical: 12,
    alignItems: 'center',
  },
  detailLabelContainer: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: '#666',
  },
  detailValueContainer: {
    flex: 2,
  },
  detailValue: {
    fontSize: 14,
    color: '#333',
    textAlign: 'right',
  },
  addressText: {
    fontSize: 13,
    color: '#666',
    fontStyle: 'italic',
  },
  // Cost details styles
  costItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  subTotal: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    marginTop: 8,
    paddingTop: 16,
  },
  total: {
    borderTopWidth: 1,
    borderTopColor: '#000',
    marginTop: 8,
    paddingTop: 16,
    fontWeight: 'bold',
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginVertical: 4,
  },
});

export default CompletedSession;
