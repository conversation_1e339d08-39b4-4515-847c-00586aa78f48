// SettingsScreen.tsx
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Switch,
  Alert,
  Platform,
  ActivityIndicator,
  ToastAndroid
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import CommonCardStyle from '../../../components/common/CommonCardStyle';
import HorizontalDivider from '../../../components/common/HorizontalDivider';
import { Colors, Fonts, Sizes } from '../../../utils/constants/Theme';
import { AppStrings } from '../../../utils/constants/AppStrings';
import { notificationSettings } from '../../../utils/templates/TemplateConfig';
import NetInfo from '@react-native-community/netinfo';
import { useAuth } from '../../../utils/configs/AuthContext';
import NotificationService from '../../../services/NotificationService';
import { AnalyticService } from '../../../utils/services/AnalyticService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  GC_SETTINGS_INAPPLICATION,
  GC_SETTINGS_INTEXT,
  GC_SETTINGS_INMAIL,
  GC_SETTINGS_USELOCATION,
  GC_FCM_TOKEN,
  GC_CUSTOMER_ID,
  GC_FETCH_LOCATION
} from '../../../utils/globals';
import { check, request, PERMISSIONS, RESULTS, Permission, PermissionStatus } from 'react-native-permissions';
import Geolocation from 'react-native-geolocation-service';
import { getDatabase, ref, set, update } from '@react-native-firebase/database';
import { getApp } from '@react-native-firebase/app';

interface LocationCoords {
  latitude: number;
  longitude: number;
}

const SettingsScreen = () => {
  const navigation = useNavigation();
  const { user, fetchUserInfo } = useAuth();
  const [settings, setSettings] = useState<Record<string, boolean>>({
    inApplication: true,
    textNotification: false,
    emailNotification: false,
    useMyLocation: false,
  });
  const [loading, setLoading] = useState(true);
  const [locationLoading, setLocationLoading] = useState(false);

  useEffect(() => {
    initializeSettings();
    checkNetworkAndPermissions();
    // Initialize location watch similar to Ionic's ionViewDidLoad
    if (user) {
      checkCurrentLocationWatch();
    }
  }, [user]);

  // Network check and permissions - similar to Ionic's ionViewCanEnter
  const checkNetworkAndPermissions = async () => {
    try {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }
      // Initialize analytics
      AnalyticService.AddAnalyticsScreenView('Settings Page', '/#/settings');
      if (user) {
        AnalyticService.AddAnalyticCustomer();
      }
    } catch (error) {
      console.error('Error checking network:', error);
    }
  };

  // Initialize settings from localStorage and Firebase - similar to Ionic's initiateValue
  const initializeSettings = async () => {
    try {
      setLoading(true);
      
      // Load settings from localStorage (similar to Ionic's initiateValue)
      const [
        storedInApplication,
        storedTextNotification,
        storedEmailNotification,
        storedUseMyLocation
      ] = await Promise.all([
        AsyncStorage.getItem(GC_SETTINGS_INAPPLICATION),
        AsyncStorage.getItem(GC_SETTINGS_INTEXT),
        AsyncStorage.getItem(GC_SETTINGS_INMAIL),
        AsyncStorage.getItem(GC_SETTINGS_USELOCATION)
      ]);

      const newSettings = {
        inApplication: storedInApplication === 'true',
        textNotification: storedTextNotification === 'true',
        emailNotification: storedEmailNotification === 'true',
        useMyLocation: storedUseMyLocation === 'true',
      };

      setSettings(newSettings);

      // If location is enabled, update current location (similar to Ionic's logic)
      if (newSettings.useMyLocation && user) {
        await updateCurrentLocation();
      }

    } catch (error) {
      console.error('Error initializing settings:', error);
      showToast('Error loading settings');
    } finally {
      setLoading(false);
    }
  };

  // Show toast message
  const showToast = (message: string) => {
    if (Platform.OS === 'android') {
      ToastAndroid.show(message, ToastAndroid.SHORT);
    } else {
      Alert.alert('Notice', message);
    }
  };

  // Handle toggle with proper event tracking
  const handleToggle = async (setting: string, value: boolean) => {
    try {
      const newSettings = { ...settings, [setting]: value };
      setSettings(newSettings);

      // Track analytics event
      AnalyticService.AddAnalyticsEvent({
        action: 'settings_toggle',
        category: 'settings',
        label: setting,
        value: value ? 1 : 0
      });

      // Handle specific setting changes
      switch (setting) {
        case 'textNotification':
          await handleTextNotificationToggle(value);
          break;
        case 'emailNotification':
          await handleEmailNotificationToggle(value);
          break;
        case 'inApplication':
          await handleInApplicationToggle(value);
          break;
        case 'useMyLocation':
          await handleLocationToggle(value);
          break;
      }
    } catch (error) {
      console.error('Error handling toggle:', error);
      // Revert on error
      setSettings(prevSettings => ({ ...prevSettings, [setting]: !value }));
      showToast('Error updating setting');
    }
  };

  // Text notification toggle - similar to Ionic's switchText
  const handleTextNotificationToggle = async (enabled: boolean) => {
    try {
      await AsyncStorage.setItem(GC_SETTINGS_INTEXT, enabled.toString());
      console.log('Text notification toggled:', enabled);
      
      if (enabled) {
        showToast('Text notifications enabled');
      } else {
        showToast('Text notifications disabled');
      }
    } catch (error) {
      console.error('Error toggling text notification:', error);
      throw error;
    }
  };

  // Email notification toggle - similar to Ionic's switchEmail
  const handleEmailNotificationToggle = async (enabled: boolean) => {
    try {
      await AsyncStorage.setItem(GC_SETTINGS_INMAIL, enabled.toString());
      console.log('Email notification toggled:', enabled);
      
      if (enabled) {
        showToast('Email notifications enabled');
      } else {
        showToast('Email notifications disabled');
      }
    } catch (error) {
      console.error('Error toggling email notification:', error);
      throw error;
    }
  };

  // In-application notification toggle - similar to Ionic's switchInApplication
  const handleInApplicationToggle = async (enabled: boolean) => {
    try {
      await AsyncStorage.setItem(GC_SETTINGS_INAPPLICATION, enabled.toString());
      
      // Note: NotificationService methods will be implemented based on actual service interface
      // For now, we'll just show success messages
      if (enabled) {
        // await NotificationService.getInstance().subscribeNotification?.();
        showToast('In-app notifications enabled');
      } else {
        // await NotificationService.getInstance().unSubscribeNotification?.();
        showToast('In-app notifications disabled');
      }
      
      // Update Firebase if user is logged in
      if (user) {
        await updateNotificationSettingsInFirebase(enabled);
      }
    } catch (error) {
      console.error('Error toggling in-app notification:', error);
      throw error;
    }
  };

  // Update notification settings in Firebase
  const updateNotificationSettingsInFirebase = async (enabled: boolean) => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) return;

      const database = getDatabase(getApp());
      await update(ref(database, `customer/${customerId}/settings/notification`), {
        'isEnable': enabled
      });
    } catch (error) {
      console.error('Error updating Firebase notification settings:', error);
    }
  };

  // Location toggle with permissions - similar to Ionic's enableBackgroundLocation
  const handleLocationToggle = async (enabled: boolean) => {
    try {
      if (enabled) {
        setLocationLoading(true);
        await checkLocationPermissionAndUpdate();
      } else {
        await AsyncStorage.setItem(GC_SETTINGS_USELOCATION, 'false');
        showToast('Location access disabled');
      }
    } catch (error) {
      console.error('Error handling location toggle:', error);
      throw error;
    } finally {
      setLocationLoading(false);
    }
  };

  // Check location permission and update - similar to Ionic's checkLocationPermissionAndUpdate
  const checkLocationPermissionAndUpdate = async () => {
    try {
      const locationPermission = await checkLocationPermission();
      
      if (locationPermission === RESULTS.GRANTED) {
        await enableLocationServices();
      } else {
        await requestLocationPermission();
      }
    } catch (error) {
      console.error('Error checking location permission:', error);
      throw error;
    }
  };

  // Check location permission
  const checkLocationPermission = async (): Promise<PermissionStatus> => {
    if (Platform.OS === 'ios') {
      return await check(PERMISSIONS.IOS.LOCATION_WHEN_IN_USE);
    } else {
      return await check(PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION);
    }
  };

  // Request location permission
  const requestLocationPermission = async () => {
    try {
      const result: PermissionStatus = await request(
        Platform.OS === 'ios'
          ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE
          : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION
      );

      if (result === RESULTS.GRANTED) {
        await enableLocationServices();
      } else if (result === RESULTS.DENIED || result === RESULTS.LIMITED) {
        // For iOS, also check for 'authorized_when_in_use'
        const permissionStatus = await checkLocationPermission();
        if (permissionStatus === RESULTS.GRANTED ||
            (Platform.OS === 'ios' && permissionStatus === 'authorized_when_in_use' as any)) {
          await enableLocationServices();
        } else {
          await showLocationAccessDialog();
        }
      } else {
        await showLocationAccessDialog();
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      throw error;
    }
  };

  // Show location access dialog - similar to Ionic's enableLocationAccess popup
  const showLocationAccessDialog = async () => {
    Alert.alert(
      'ENABLE LOCATION ACCESS',
      'Please enable location access to get mechanic available for your area',
      [
        {
          text: 'CANCEL',
          style: 'cancel',
          onPress: async () => {
            setSettings(prev => ({ ...prev, useMyLocation: false }));
            await AsyncStorage.setItem(GC_SETTINGS_USELOCATION, 'false');
          }
        },
        {
          text: 'ENABLE',
          onPress: async () => {
            try {
              await requestLocationPermission();
            } catch (error) {
              // If permission still not granted, try to open settings
              Alert.alert(
                'Location Access Required',
                'Please enable location access in your device settings.',
                [
                  { text: 'OK', style: 'default' }
                ]
              );
            }
          }
        }
      ]
    );
  };

  // Enable location services
  const enableLocationServices = async () => {
    try {
      await AsyncStorage.setItem(GC_SETTINGS_USELOCATION, 'true');
      await updateCurrentLocation();
      
      // Update Firebase location settings
      if (user) {
        const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
        if (customerId) {
          const database = getDatabase(getApp());
          await set(ref(database, `customer/${customerId}/settings/use-location`), true);
        }
      }
      
      showToast('Location access enabled');
    } catch (error) {
      console.error('Error enabling location services:', error);
      throw error;
    }
  };

  // Update current location - similar to Ionic's updateCurrentLocation
  const updateCurrentLocation = async (): Promise<LocationCoords | null> => {
    return new Promise((resolve, reject) => {
      const options = {
        enableHighAccuracy: true,
        timeout: 20000,
        maximumAge: 0
      };

      Geolocation.getCurrentPosition(
        async (position: any) => {
          try {
            const coords = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            };
            
            // Store in localStorage
            await AsyncStorage.setItem(GC_FETCH_LOCATION, JSON.stringify(coords));
            
            // Update Firebase if user is logged in
            if (user) {
              const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
              if (customerId) {
                const database = getDatabase(getApp());
                await set(ref(database, `customer/${customerId}/current-location`), coords);
              }
            }
            
            resolve(coords);
          } catch (error) {
            console.error('Error storing location:', error);
            resolve(null);
          }
        },
        (error: any) => {
          console.error('Error getting location:', error);
          reject(error);
        },
        options
      );
    });
  };

  // Check user exist - similar to Ionic's isUserExist
  const isUserExist = async (): Promise<boolean> => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      return !!customerId;
    } catch (error) {
      console.error('Error checking user existence:', error);
      return false;
    }
  };

  // Check current location watch - similar to Ionic's checkCurrentLocationWatch
  const checkCurrentLocationWatch = async () => {
    try {
      console.log('checkCurrentLocationWatch method called');
      
      if (Platform.OS === 'web') {
        console.log('Web platform, skipping location check');
        return;
      }

      const locationEnabled = await checkLocationEnabled();
      if (!locationEnabled) {
        await showLocationAccessDialog();
      } else {
        await updateCurrentLocation();
        if (await isUserExist()) {
          await updateLocationSettingsInFirebase(true);
        }
      }
    } catch (error) {
      console.error('Error in checkCurrentLocationWatch:', error);
    }
  };

  // Check if location services are enabled
  const checkLocationEnabled = async (): Promise<boolean> => {
    try {
      // This is a simplified version. In a real app, you'd use a native module
      // to check if location services are enabled on the device
      const permission = await checkLocationPermission();
      return permission === RESULTS.GRANTED;
    } catch (error) {
      console.error('Error checking location enabled:', error);
      return false;
    }
  };

  // Update location settings in Firebase - similar to Ionic's updateLocationSettings
  const updateLocationSettingsInFirebase = async (enabled: boolean) => {
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) return;

      const database = getDatabase(getApp());
      await set(ref(database, `customer/${customerId}/settings/use-location`), enabled);
    } catch (error) {
      console.error('Error updating location settings in Firebase:', error);
    }
  };

  // Go to location settings - similar to Ionic's goToLocationSettings
  const goToLocationSettings = () => {
    Alert.alert(
      'Location Access Required',
      'Please enable location access in your device settings to get the best experience.',
      [
        {
          text: 'Cancel',
          style: 'cancel'
        },
        {
          text: 'Settings',
          onPress: () => {
            // In a real implementation, you'd use Linking.openSettings()
            console.log('Would open device location settings');
          }
        }
      ]
    );
  };

  if (loading) {
    return (
      <ScreenLayout useScrollView={false} centerContent={true}>
        <ActivityIndicator size="large" color={Colors.PRIMARY} />
      </ScreenLayout>
    );
  }

  return (
    <ScreenLayout
      useScrollView={true}
      useImageBackground={true}
      centerContent={false}
      topPadding={18}>
        <View style={styles.container}>
        <CommonCardStyle
          header={AppStrings.MCX_GPS_SETTINGS_TEXT}
          headerColor={Colors.SECONDARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          isCardContainerDecorated={false}
          isTitleBordered={true}
          cardBackgroundColor="transparent"
          style={styles.cardContent}
          marginVertical={8}
        >
          <View style={styles.row}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>{AppStrings.MCX_USE_MY_LOCATION_TITLE}</Text>
              {locationLoading && (
                <ActivityIndicator
                  size="small"
                  color={Colors.PRIMARY}
                  style={styles.loader}
                />
              )}
            </View>
            <Switch
              value={settings.useMyLocation}
              onValueChange={(value) => handleToggle('useMyLocation', value)}
              trackColor={{ false: '#ccc', true: Colors.PRIMARY }}
              thumbColor={settings.useMyLocation ? Colors.PRIMARY : '#f4f3f4'}
              disabled={locationLoading}
            />
          </View>
        </CommonCardStyle>

        <CommonCardStyle
          header={AppStrings.MCX_NOTIFICATION_TITLE}
          headerColor={Colors.SECONDARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          isCardContainerDecorated={false}
          isTitleBordered={true}
          cardBackgroundColor="transparent"
          style={styles.cardContent}
        >
          {notificationSettings.map((setting, index) => (
            <React.Fragment key={index}>
              <View style={styles.row}>
                <Text style={styles.label}>{setting.label}</Text>
                <Switch
                  value={settings[setting.value]}
                  onValueChange={(value) => handleToggle(setting.value, value)}
                  trackColor={{ false: '#ccc', true: Colors.PRIMARY }}
                  thumbColor={settings[setting.value] ? Colors.PRIMARY : '#f4f3f4'}
                />
              </View>
              <HorizontalDivider isFullWidth={true} />
            </React.Fragment>
          ))}

        </CommonCardStyle>
      </View>
    </ScreenLayout>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
  },
  cardContent: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    margin: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 14,
  },
  label: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_COMPONENT_TEXT_COLOR,
    opacity: 0.8,
    fontWeight: '600',
    paddingHorizontal: 12,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  loader: {
    marginLeft: 10,
  },
  divider: {
    marginLeft: 0,
    marginRight: 0
  }
});

export default SettingsScreen;