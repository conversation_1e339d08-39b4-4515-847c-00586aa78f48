import React, {useState, useEffect, useCallback} from 'react';
import {View, Text, StyleSheet, Alert, ActivityIndicator} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import ScreenLayout from '../../../components/layout/ScreenLayout';
import TitleSection from '../../../components/common/TitleSection';
import CommonTextInput from '../../../components/common/CommonTextInput';
import CustomButton from '../../../components/common/CustomButton';
import {Colors, Fonts, Sizes} from '../../../utils/constants/Theme';
import {
  RouteNames,
  ExceptionStrings,
} from '../../../utils/constants/AppStrings';
import database from '@react-native-firebase/database';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_CUSTOMER_ID} from '../../../utils/globals';
import AppBar from '../../../components/common/AppBar';
import {VIN_URL} from '../../../constants/constants';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import NetInfo from '@react-native-community/netinfo';

interface VehicleInfo {
  vin: string;
  make: string;
  model: string;
  manufactureYear: string;
  fuel: string;
  warning: string;
  error: string;
  valid: boolean;
}

const VehicleEditPage = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const params = route.params as any;

  const [vinNumber, setVinNumber] = useState('');
  const [vehicleInfo, setVehicleInfo] = useState<VehicleInfo>({
    vin: '',
    make: '',
    model: '',
    manufactureYear: '',
    fuel: '',
    warning: '',
    error: '',
    valid: false,
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [removing, setRemoving] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [adding, setAdding] = useState(false);
  const [editProfile, setEditProfile] = useState(false);
  const [existingVehicleInfo, setExistingVehicleInfo] = useState<any>(null);

  const insets = useSafeAreaInsets();

  const handleMailPress = () => {
    navigation.navigate(RouteNames.MCX_NAV_MESSAGES as never);
  };

  useEffect(() => {
    // Network check - similar to Ionic's ionViewCanEnter
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      setParams();
    };

    checkNetworkAndLoad();
  }, [params, navigation]);

  const setParams = useCallback(() => {
    setEditProfile(false);
    setExistingVehicleInfo(null);
    const vehicle = params?.['vehicle-info'];
    if (vehicle) {
      setExistingVehicleInfo(vehicle);
      setEditProfile(true);
      // Pre-fill VIN for editing
      if (vehicle['vehicle-detail']?.vin) {
        setVinNumber(vehicle['vehicle-detail'].vin);
        // Load existing vehicle details
        setVehicleInfo({
          vin: vehicle['vehicle-detail'].vin,
          make: vehicle['vehicle-detail'].make || '',
          model: vehicle['vehicle-detail'].model || '',
          manufactureYear: vehicle['vehicle-detail']['manufacture-year'] || '',
          fuel: vehicle['vehicle-detail'].fuel || '',
          warning: vehicle['vehicle-detail'].warning || '',
          error: '',
          valid: true,
        });
      }
    }
  }, [params]);

  const searchVIN = async (vin: string) => {
    const FORMAT = '?format=json';
    try {
      const response = await fetch(VIN_URL + vin + FORMAT);
      const data = await response.json();
      console.log('vin data:', data);
      return data;
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch VIN data');
      return null;
    }
  };

  const processVIN = (vinApiData: any, vin: string) => {
    let vinInfo = {
      vin: vin,
      make: '',
      model: '',
      manufactureYear: '',
      fuel: '',
      warning: '',
      error: '',
      valid: false,
    };

    for (let vinIndex = 0; vinIndex < vinApiData.Count; vinIndex++) {
      switch (vinApiData.Results[vinIndex].VariableId) {
        case 26:
          vinInfo.make = vinApiData.Results[vinIndex].Value;
          break;
        case 28:
          vinInfo.model = vinApiData.Results[vinIndex].Value;
          break;
        case 29:
          vinInfo.manufactureYear = vinApiData.Results[vinIndex].Value;
          break;
        case 24:
          vinInfo.fuel = vinApiData.Results[vinIndex].Value;
          break;
        case 143:
          vinInfo.warning = vinApiData.Results[vinIndex].Value;
          break;
        case 156:
          vinInfo.error = vinApiData.Results[vinIndex].Value
            ? 'The Model Year decoded for this VIN may be incorrect'
            : '';
          break;
        default:
          break;
      }
    }

    if (!vinInfo.error) {
      if (!vinInfo.make) {
        vinInfo.error = 'The Model Year decoded for this VIN may be incorrect';
        vinInfo.valid = false;
        return vinInfo;
      }
      vinInfo.valid = true;
      return vinInfo;
    } else {
      vinInfo.valid = false;
      return vinInfo;
    }
  };

  const loadVIN = async () => {
    // VIN validation similar to Ionic reactive forms
    const vinPattern = /^[a-zA-Z0-9]+$/;
    if (!vinNumber.trim()) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        'VIN is required',
      );
      return;
    }
    if (!vinPattern.test(vinNumber)) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        'VIN must contain only alphanumeric characters',
      );
      return;
    }
    if (vinNumber.length !== 17) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }

    if (params?.['vehicle-array']) {
      const vehicleArray = params['vehicle-array'];
      const alreadyExists = vehicleArray.some(
        (vehicle: any) => {
          if (editProfile && existingVehicleInfo && vehicle['vehicle-id'] === existingVehicleInfo['vehicle-id']) {
            return false;
          }
          // Case-insensitive VIN comparison
          return vehicle['vehicle-detail']?.vin?.toLowerCase() === vinNumber.toLowerCase();
        },
      );
      if (alreadyExists) {
        Alert.alert('VEHICLE EXISTS', 'This VIN is already added');
        return;
      }
    }

    setLoading(true);
    const vinApiData = await searchVIN(vinNumber);
    if (vinApiData) {
      const processedVin = processVIN(vinApiData, vinNumber);
      console.log('vehcle info:', processedVin);
      setVehicleInfo(processedVin);
      if (!processedVin.valid) {
        Alert.alert('VIN Error', processedVin.error || 'Invalid VIN data');
      } else {
        Alert.alert('Success', 'VIN loaded successfully');
      }
    }
    setLoading(false);
  };

  const addVehicle = async () => {
    if (!vehicleInfo.valid) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }

    setAdding(true);
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId) {
        Alert.alert('Error', 'Customer ID not found');
        return;
      }

      const vehicleDetail = {
        vin: vehicleInfo.vin,
        make: vehicleInfo.make,
        model: vehicleInfo.model,
        'manufacture-year': vehicleInfo.manufactureYear,
        fuel: vehicleInfo.fuel,
        warning: vehicleInfo.warning,
      };

      const vehicleRef = database()
        .ref('customer')
        .child(customerId)
        .child('myvehicles')
        .push();
      await vehicleRef.set(vehicleDetail);
      
      // Success feedback
      Alert.alert(
        'Success',
        'Vehicle added successfully!',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error adding vehicle:', error);
      Alert.alert('Error', 'Failed to add vehicle. Please try again.');
    } finally {
      setAdding(false);
    }
  };

  const updateVehicle = async () => {
    if (!vehicleInfo.valid) {
      Alert.alert(
        ExceptionStrings.MCX_EXCEPTION_ERROR_LABEL,
        ExceptionStrings.MCX_EXCEPTION_ENTER_VALID_VIN_LABEL,
      );
      return;
    }

    setUpdating(true);
    try {
      const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
      if (!customerId || !existingVehicleInfo) {
        Alert.alert('Error', 'Customer ID or vehicle information not found');
        return;
      }

      const vehicleId = existingVehicleInfo['vehicle-id'];
      const vehicleDetail = {
        vin: vehicleInfo.vin,
        make: vehicleInfo.make,
        model: vehicleInfo.model,
        'manufacture-year': vehicleInfo.manufactureYear,
        fuel: vehicleInfo.fuel,
        warning: vehicleInfo.warning,
      };

      await database()
        .ref('customer')
        .child(customerId)
        .child('myvehicles')
        .child(vehicleId)
        .update(vehicleDetail);
      
      // Success feedback
      Alert.alert(
        'Success',
        'Vehicle updated successfully!',
        [{ text: 'OK', onPress: () => navigation.goBack() }]
      );
    } catch (error) {
      console.error('Error updating vehicle:', error);
      Alert.alert('Error', 'Failed to update vehicle. Please try again.');
    } finally {
      setUpdating(false);
    }
  };

  const removeVehicle = async () => {
    Alert.alert(
      'Remove Vehicle',
      'Are you sure you want to remove this vehicle?',
      [
        {text: 'Cancel', style: 'cancel'},
        {
          text: 'REMOVE',
          style: 'destructive',
          onPress: async () => {
            setRemoving(true);
            try {
              const customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
              if (!customerId || !existingVehicleInfo) {
                Alert.alert('Error', 'Customer ID or vehicle information not found');
                return;
              }
              const vehicleId = existingVehicleInfo['vehicle-id'];
              await database()
                .ref('customer')
                .child(customerId)
                .child('myvehicles')
                .child(vehicleId)
                .remove();
              
              // Success feedback
              Alert.alert(
                'Success',
                'Vehicle removed successfully!',
                [{ text: 'OK', onPress: () => navigation.goBack() }]
              );
            } catch (error) {
              console.error('Error removing vehicle:', error);
              Alert.alert('Error', 'Failed to remove vehicle. Please try again.');
            } finally {
              setRemoving(false);
            }
          },
        },
      ],
    );
  };

  return (
    <View style={[styles.mainContainer, {paddingTop: insets.top}]}>
      <AppBar
        showBackButton={false}
        showMailIcon={false}
        showChatIcon={false}
        showMenuIcon={false}
        showLogo={true}
        onMailPress={handleMailPress}
      />
      <TitleSection
        title={editProfile ? 'EDIT VEHICLE' : 'ADD VEHICLE'}
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
        onBack={() => navigation.goBack()}
      />
      <ScreenLayout
        useScrollView={true}
        useImageBackground={true}
        centerContent={false}
        useHorizontalPadding={true}
        scrollContainerStyle={{paddingBottom: 80}}>
        <View style={styles.formContainer}>
          <Text style={styles.fieldLabel}>VIN</Text>
          <CommonTextInput
            value={vinNumber}
            onChangeText={setVinNumber}
            placeholder="Enter VIN number"
            style={styles.vinInput}
            maxLength={17}
          />
          <CustomButton
            text={loading ? 'Loading...' : 'Load VIN'}
            onPress={loadVIN}
            variant="primary"
            size="small"
            backgroundColor={Colors.PRIMARY}
            textColor="#fff"
            style={styles.loadVinButton}
            disabled={loading}
          />

          {vehicleInfo.error && (
            <View style={styles.errorCard}>
              <Text style={styles.errorTitle}>WRONG VIN!</Text>
              <Text style={styles.errorText}>{vehicleInfo.error}</Text>
            </View>
          )}
        </View>
        {vehicleInfo.valid && (
          <View style={styles.vehicleDetailsCard}>
            <View style={styles.vehicleDetailRow}>
              <Text style={styles.vehicleDetailLabel}>VIN :</Text>
              <Text style={styles.vehicleDetailValue}>{vehicleInfo.vin}</Text>
            </View>
            <View style={styles.vehicleDetailRow}>
              <Text style={styles.vehicleDetailLabel}>Make :</Text>
              <Text style={styles.vehicleDetailValue}>{vehicleInfo.make}</Text>
            </View>
            <View style={styles.vehicleDetailRow}>
              <Text style={styles.vehicleDetailLabel}>Model :</Text>
              <Text style={styles.vehicleDetailValue}>{vehicleInfo.model}</Text>
            </View>
            <View style={styles.vehicleDetailRow}>
              <Text style={styles.vehicleDetailLabel}>Fuel :</Text>
              <Text style={styles.vehicleDetailValue}>{vehicleInfo.fuel}</Text>
            </View>
            <View style={styles.vehicleDetailRow}>
              <Text style={styles.vehicleDetailLabel}>
                Year of manufacture :
              </Text>
              <Text style={styles.vehicleDetailValue}>
                {vehicleInfo.manufactureYear}
              </Text>
            </View>
          </View>
        )}
        {vehicleInfo.valid && (
          <View style={styles.buttonContainer}>
            {!editProfile ? (
              <CustomButton
                text={adding ? 'Adding...' : 'Add vehicle'}
                onPress={addVehicle}
                variant="primary"
                size="large"
                fullWidth={true}
                backgroundColor={Colors.PRIMARY}
                textColor="#fff"
                disabled={adding}
              />
            ) : (
              <View style={styles.editButtons}>
                <CustomButton
                  text={removing ? 'Removing...' : 'Remove vehicle'}
                  onPress={removeVehicle}
                  variant="secondary"
                  size="large"
                  fullWidth={false}
                  style={styles.removeButton}
                  disabled={removing || updating}
                />
                <CustomButton
                  text={updating ? 'Updating...' : 'Update vehicle'}
                  onPress={updateVehicle}
                  variant="primary"
                  size="large"
                  fullWidth={false}
                  style={styles.updateButton}
                  disabled={removing || updating}
                />
              </View>
            )}
          </View>
        )}

        {(loading || adding || removing || updating) && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.PRIMARY} />
          </View>
        )}
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 0,
  },
  formContainer: {
    backgroundColor: 'white',
    marginTop: 20,
    padding: 20,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  fieldLabel: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_BOLD,
    color: Colors.SECONDARY,
    marginTop: 16,
    marginBottom: 8,
  },
  vinInput: {
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    borderRadius: 4,
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    backgroundColor: '#FFFFFF',
  },
  loadVinButton: {
    borderRadius: 2,
    paddingVertical: 10,
    minHeight: 'auto',
    marginTop: 14,
    marginBottom: 10,
  },
  errorCard: {
    backgroundColor: '#ffebee',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#f44336',
  },
  errorTitle: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_BOLD,
    color: '#f44336',
    marginBottom: 8,
  },
  errorText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: '#f44336',
  },
  buttonContainer: {
    marginTop: 20,
  },
  editButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  removeButton: {
    flex: 1,
    marginRight: 10,
    backgroundColor: Colors.SECONDARY,
  },
  updateButton: {
    flex: 1,
    marginLeft: 10,
  },
  vehicleDetailsCard: {
    backgroundColor: '#eee',
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
  },
  vehicleDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  vehicleDetailLabel: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_BOLD,
    color: '#3b4b5e',
    flex: 1,
  },
  vehicleDetailValue: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: '#3b4b5e',
    flex: 1,
    textAlign: 'right',
    fontWeight: 'bold',
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default VehicleEditPage;
