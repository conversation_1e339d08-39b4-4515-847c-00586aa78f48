import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  SafeAreaView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useRoute, useNavigation } from '@react-navigation/native';
import {AppStrings} from '../../utils/constants/strings';
import {AppCommonIcons} from '../../utils/constants/AppStrings';
import AppBackground from '../../components/ui/AppBackground';
import { PageHeader } from '../../components/ui/PageHeader';
import { THEME } from '../../utils/constants/Theme';
import { useAuth } from '../../utils/configs/AuthContext';
import { AnalyticService } from '../../utils/services/AnalyticService';
import { getDatabase, ref } from '@react-native-firebase/database';
import Icon from 'react-native-vector-icons/Ionicons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import NetInfo from '@react-native-community/netinfo';

const HelpDetailsScreen = () => {
  const route = useRoute();
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const { user } = useAuth();
  const [faqDetail, setFaqDetail] = useState<any>(null);
  const [loaderFAQDetail, setLoaderFAQDetail] = useState(false);

  useEffect(() => {
    const helpKey = (route.params as any)?.['help-key'];
    if (!helpKey) {
      navigation.goBack();
      return;
    }

    // Network check
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      // Auth check
      if (!user) {
        (navigation as any).navigate('Login');
        return;
      }

      // Google Analytics
      AnalyticService.AddAnalyticsScreenView('HelpDetail Page', '/#/help-detail');

      fetchFAQDetail(helpKey);
    };

    checkNetworkAndLoad();
  }, [route.params, navigation, user]);

  /**
   * Fetch particular FAQ details.
   */
  const fetchFAQDetail = (helpKey: string) => {
    setLoaderFAQDetail(false);
    const db = getDatabase();
    const faqRef = ref(db, `faq/customer/${helpKey}`);
    faqRef.on('value', faqDetailSnapshot => {
      setLoaderFAQDetail(false);
      setFaqDetail(faqDetailSnapshot.val());
      setLoaderFAQDetail(true);
    });
  };

  return (
    <SafeAreaView
      style={[
        styles.container,
        { paddingTop: insets.top },
        { paddingBottom: 0 },
      ]}
    >
      <AppBackground />
      
      {/* Custom Header with Logo and Back Button */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Image
          source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>

      <PageHeader
        title={AppStrings.HELP_STRINGS.HEADERS.HELP}
      />

      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
      >
        <View style={styles.section}>
          {loaderFAQDetail ? (
            !faqDetail ? (
              <Text style={styles.noDataText}>No FAQ details available.</Text>
            ) : (
              <View style={styles.faqDetailContainer}>
                <Text style={styles.questionText}>{faqDetail.question}</Text>
                <View style={styles.dividerLine} />
                <Text style={styles.answerText}>{faqDetail.answer}</Text>
              </View>
            )
          ) : (
            <ActivityIndicator size="large" color={THEME.COLORS.PRIMARY_RED} />
          )}
        </View>
        {/* <View style={styles.supportSection}>
          <Text style={styles.supportTitle}>Need more help?</Text>
          <Text style={styles.supportText}>
            Contact our customer support <NAME_EMAIL> or call us at 1-800-555-0123.
          </Text>
        </View> */}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  header: {
    height: 56,
    backgroundColor: '#1f2a38',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 15,
  },
  backButton: {
    position: 'absolute',
    left: 15,
    padding: 8,
    zIndex: 10,
  },
  logo: {
    width: 120,
    height: 40,
    resizeMode: 'contain',
    tintColor: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    backgroundColor: '#FFFFFF',
    margin: 10,
    padding: 5,
    paddingBottom: 180,
    marginBottom: 50,
  },
  section: {
    backgroundColor: 'transparent',
  },
  faqDetailContainer: {
    padding: 16,
  },
  questionText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  dividerLine: {
    height: 1,
    backgroundColor: '#000',
    marginBottom: 16,
  },
  answerText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  supportSection: {
    marginTop: 8,
    marginBottom: 24,
    padding: 20,
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#C00000',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  supportTitle: {
    fontSize: 17,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  supportText: {
    fontSize: 15,
    color: '#666666',
    lineHeight: 22,
  },
  noDataText: {
    fontSize: 14,
    color: '#999',
    textAlign: 'center',
    paddingVertical: 20,
  },
});

export default HelpDetailsScreen;