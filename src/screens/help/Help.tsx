import React, { useEffect, useState } from 'react';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ScreenLayout from '../../components/layout/ScreenLayout';
import TitleSection from '../../components/common/TitleSection';
import CommonCardStyle from '../../components/common/CommonCardStyle';
import { Colors, Sizes } from '../../utils/constants/Theme';
import { AppStrings } from '../../utils/constants/AppStrings';
import CustomListItem from '../../components/common/CustomHelpListItem';
import { getDatabase, ref } from '@react-native-firebase/database';
import { useAuth } from '../../utils/configs/AuthContext';
import NetInfo from '@react-native-community/netinfo';
import { AnalyticService } from '../../utils/services/AnalyticService';

const Help = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const [faqTitle, setFaqTitle] = useState<any>(null);
  const [loaderAllFAQ, setLoaderAllFAQ] = useState(false);

  useEffect(() => {
    // Google Analytics
    AnalyticService.AddAnalyticsScreenView('Help Page', '/#/help');

    // Network check
    const checkNetworkAndLoad = async () => {
      const state = await NetInfo.fetch();
      if (!state.isConnected) {
        console.log("Network check failed: ", state.isConnected);
        navigation.navigate('NetworkFailedPage' as never);
        return;
      }

      // Auth check
      if (!user) {
        (navigation as any).navigate('Login');
        return;
      }

      getAllFAQ();
    };

    checkNetworkAndLoad();
  }, [user, navigation]);

  /**
   * Fetch all F.A.Q from firebase.
   */
  const getAllFAQ = () => {
    setLoaderAllFAQ(false);
    const db = getDatabase();
    const faqRef = ref(db, 'faq/customer');
    faqRef.on('value', faq => {
      setLoaderAllFAQ(false);
      setFaqTitle(faq.val());
      setLoaderAllFAQ(true);
    });
  };

  /**
   * Navigate to help details page
   */
  const handleHelpItemPress = (helpKey: string) => {
    (navigation as any).navigate('HelpDetails', { 'help-key': helpKey });
  };

  const renderHelpItems = () => {
    if (!faqTitle) return null;
    const faqArray = Object.keys(faqTitle).map(key => ({
      ...faqTitle[key],
      indexKey: key,
    }));

    return faqArray.map((item, index) => (
      <View key={item.indexKey} style={styles.helpItemContainer}>
        <CustomListItem
          title={item.question}
          onPress={() => handleHelpItemPress(item.indexKey)}
          titleColor={Colors.COMMON_GREY_SHADE_LIGHT}
          titleSize={Sizes.MEDIUM}
          horizontalDivider={true}
          iconStyle={styles.cardIconStyle}
          backgroundColor={Colors.COMMON_WHITE_SHADE}
        />
        {index < faqArray.length - 1 && <View style={styles.itemGap} />}
      </View>
    ));
  };

  return (
    <View style={styles.mainContainer}>
      <ScreenLayout useScrollView={true} centerContent={true}>
        {/* Title Section */}
        <TitleSection
          title={AppStrings.MCX_HELP_TITLE}
          bgColor={Colors.PRIMARY}
          textColor="#fff"
          style={styles.titleSection}
          onBack={() => navigation.goBack()}
        />

        {/* FAQ Card with Help Items */}
        <CommonCardStyle
          header={AppStrings.MCX_HELP_FAQ_SECTION}
          headerColor={Colors.SECONDARY}
          textColor={Colors.COMMON_WHITE_SHADE}
          isCardContainerDecorated={true}
          isTitleBordered={true}
          cardBackgroundColor="transparent"
        >
          <View style={styles.helpItemsContainer}>
            {!loaderAllFAQ && (
              <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color={Colors.PRIMARY} />
              </View>
            )}
            {faqTitle && loaderAllFAQ && renderHelpItems()}
            {!faqTitle && loaderAllFAQ && (
              <Text style={styles.noDataText}>No FAQ available.</Text>
            )}
          </View>
        </CommonCardStyle>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 8,
  },
  helpItemContainer: {
    width: '100%',
    backgroundColor: 'transparent',
  },
  helpItemsContainer: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  itemGap: {
    height: 2,
  },
  cardIconStyle: {
    width: 24,
    height: 24,
    tintColor: Colors.PRIMARY,
  },
  loaderContainer: {
    paddingVertical: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  noDataText: {
    textAlign: 'center',
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    paddingVertical: 24,
  },
});

export default Help;