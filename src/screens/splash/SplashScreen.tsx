import React, { useEffect } from 'react';
import { View, Image, StyleSheet, Platform } from 'react-native';
import { hp, wp } from '../../utils/ResponsiveParams';
import { StackNavigationProp } from '@react-navigation/stack';
import { AppCommonIcons } from '../../utils/constants/AppStrings';
import Geolocation from 'react-native-geolocation-service';
import { useAuth } from '../../utils/configs/AuthContext';
import { VehicleService } from '../../utils/services/VehicleService';
import { RouteNames } from '../../utils/constants/AppStrings';
import { request, check, PERMISSIONS, RESULTS } from 'react-native-permissions';

type RootStackParamList = {
  LoginMainScreen: undefined;
  [key: string]: undefined;
};

type SplashScreenProps = {
  navigation: StackNavigationProp<RootStackParamList, 'LoginMainScreen'>;
};

const SplashScreen: React.FC<SplashScreenProps> = ({ navigation }) => {
  const { user } = useAuth();

   useEffect(() => {
    // Only request location permission on initial app launch, not on every navigation
    if (user) {
      checkAndRequestPermission();
    } else {
      // For non-logged in users, just navigate after a short delay
      setTimeout(() => {
        navigateToNextScreen();
      }, 1000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user]);
  const navigateToNextScreen = () => {
    if (user) {
      (navigation as any).replace(RouteNames.MCX_NAV_DashBoard);
    } else {
      (navigation as any).replace(RouteNames.MCX_NAV_LoginMainScreen);
    }
  };

const checkAndRequestPermission = async () => {
  try {
    const permission = Platform.OS === 'ios' ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
    let status = await check(permission);
    if (status !== RESULTS.GRANTED) {
      status = await request(permission);
    }
    if (status === RESULTS.GRANTED) {
      console.log('Location permission granted');
      getCurrentLocation();
    } else if (status === RESULTS.DENIED) {
      console.log('Location permission denied');
    } else if (status === RESULTS.BLOCKED) {
      console.log('Location permission blocked');
    }
    setTimeout(() => {
      navigateToNextScreen();
    }, 1000);
  } catch (error) {
    console.log('Error checking/requesting location permission:', error);
    setTimeout(() => {
      navigateToNextScreen();
    }, 1000);
  }
};

  const getCurrentLocation = () => {
    Geolocation.getCurrentPosition(
    async (position) => {
      console.log('Current position:', position);
      if (user?.uid) {
        try {
          await VehicleService.updateCustomerLocation(user.uid, {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          });
          console.log('Customer location updated successfully');
        } catch (error) {
          console.error('Error updating customer location:', error);
        }
      }
    },
    (error) => {
      console.log('Error getting location:', error);
    },
    { enableHighAccuracy: true, timeout: 15000, maximumAge: 10000 }
  );
};

  return (
    <View style={styles.container}>
      <Image
        source={AppCommonIcons.MCX_APP_ICON}
        style={styles.logo}
        resizeMode="contain"
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logo: {
    width: wp(60),
    height: hp(30),
  },
});

export default SplashScreen;
