import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import ScreenLayout from '../../components/layout/ScreenLayout';
import TitleSection from '../../components/common/TitleSection';
import CustomButton from '../../components/common/CustomButton';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';
import { AppCommonIcons, AppStrings } from '../../utils/constants/AppStrings';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const NetworkFailedPage = () => {
  const navigation = useNavigation();
  const insets = useSafeAreaInsets();
  const handleRetry = () => {
    console.log('NetworkFailedPage: Retry button pressed');
    // Check network connectivity and navigate back if online
    // For now, just go back
    navigation.goBack();
  };

  return (
    <View style={[styles.mainContainer, {paddingTop: insets.top}]}>
      <TitleSection
        title="Connection Failed"
        bgColor={Colors.PRIMARY}
        textColor="#fff"
        style={styles.titleSection}
      />

      <ScreenLayout
        useScrollView={false}
        useImageBackground={true}
        centerContent={true}
        useHorizontalPadding={true}
      >
        <View style={styles.contentContainer}>
          <Image
            source={AppCommonIcons.MCX_EXCEPTION_NO_DATA_FOUND_ICON}
            style={styles.errorIcon}
          />

          <Text style={styles.title}>No Internet Connection</Text>
          <Text style={styles.message}>
            Please check your internet connection and try again.
          </Text>

          <CustomButton
            text="Retry"
            onPress={handleRetry}
            variant="primary"
            size="large"
            fullWidth={true}
            backgroundColor={Colors.SECONDARY}
            textColor="#fff"
            isBoldText={true}
            style={styles.retryButton}
          />
        </View>
      </ScreenLayout>
    </View>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  titleSection: {
    marginBottom: 0,
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorIcon: {
    width: 100,
    height: 100,
    marginBottom: 30,
    tintColor: Colors.PRIMARY,
  },
  title: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_BOLD,
    color: Colors.SECONDARY,
    textAlign: 'center',
    marginBottom: 16,
  },
  message: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  retryButton: {
    marginTop: 20,
  },
});

export default NetworkFailedPage;