import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity} from 'react-native';
import AppBackground from '../../components/ui/AppBackground';
import {useNavigation} from '@react-navigation/native';
import type {StackNavigationProp} from '@react-navigation/stack';
import {AppStrings} from '../../utils/constants/strings';
import {PageHeader} from '../../components/ui/PageHeader';
import SectionHeader from '../../components/ui/SectionHeader';
import {THEME} from '../../utils/constants/Theme';
type LegalStackParamList = {
  PrivacyPolicyScreen: undefined;
  TermsConditionsScreen: undefined;
};

const LegalScreen = () => {
  const navigation = useNavigation<StackNavigationProp<LegalStackParamList>>();
  return (
    <>
      <View style={styles.container}>
        <AppBackground />
        <PageHeader title={AppStrings.HELP_STRINGS.HEADERS.LEGAL} />

        <View style={styles.content}>
          <View style={styles.section}>
            <SectionHeader
              title={AppStrings.HELP_STRINGS.SECTIONS.LEGAL_DOCUMENTS}
              headerTextColor={THEME.COLORS.ICON_GREY_TINT}
            />

            <View style={styles.sectionContent}>
              <TouchableOpacity
                style={styles.row}
                onPress={() => navigation.navigate('PrivacyPolicyScreen')}
                activeOpacity={0.7}>
                <Text style={styles.rowText}>Privacy Policy</Text>
                <Text style={styles.arrow}>{'>'}</Text>
              </TouchableOpacity>
              <View style={styles.divider} />
              <TouchableOpacity
                style={styles.row}
                onPress={() => navigation.navigate('TermsConditionsScreen')}
                activeOpacity={0.7}>
                <Text style={styles.rowText}>Terms & Conditions</Text>
                <Text style={styles.arrow}>{'>'}</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'transparent',
  },
  section: {
    marginTop: 60,
    marginHorizontal: 16,
  },
  sectionHeader: {
    color: '#444C56',
    fontSize: 18,
    fontWeight: '500',
    marginBottom: 12,
    backgroundColor: 'rgba(30,42,54,0.9)',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderTopLeftRadius: 2,
    borderTopRightRadius: 2,
    letterSpacing: 1,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 2,
    paddingHorizontal: 0,
    paddingVertical: 0,
    borderWidth: 3,
    borderColor: '#222',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 22,
    paddingHorizontal: 16,
    backgroundColor: '#fff',
  },
  rowText: {
    fontSize: 19,
    color: '#222',
  },
  arrow: {
    fontSize: 28,
    color: '#888',
    marginLeft: 8,
  },
  divider: {
    height: 1,
    backgroundColor: '#eee',
    marginHorizontal: 0,
  },
  sectionContent: {
    backgroundColor: '#000',
    padding: 5,
  },
  content: {
    flex: 1,
    paddingTop: 20,
  },
});

export default LegalScreen;
