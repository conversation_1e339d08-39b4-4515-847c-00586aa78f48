// Add this to your main App.tsx or root component
import React, {useEffect} from 'react';
import FcmForeground from '../components/message/firebaseForground';
import NotificationService from '../services/NotificationService';
import {navigationRef} from '../navigations/navigationRef';

export const NotificationSetup: React.FC = () => {
  useEffect(() => {
    // Initialize NotificationService with navigation reference
    const notificationService = NotificationService.getInstance();
    notificationService.setNavigationRef(navigationRef);
    
    console.log('🔔 Notification service initialized');
  }, []);

  // This component doesn't render anything visible
  return <FcmForeground />;
};