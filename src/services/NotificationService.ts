import messaging, {
  FirebaseMessagingTypes,
} from '@react-native-firebase/messaging';
import {Alert} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {navigate} from '../navigations/navigationRef';
import {RouteNames} from '../utils/constants/AppStrings';
import {NOTIFICATION_TYPE} from '../utils/globals';

export interface NotificationData {
  id: string;
  title: string;
  body: string;
  data?: {[key: string]: string | object};
  timestamp: number;
  read: boolean;
}

class NotificationService {
  private static instance: NotificationService;
  private notifications: NotificationData[] = [];
  private pendingNotification: FirebaseMessagingTypes.RemoteMessage | null = null;
  private navigationRef: any = null;

  private constructor() {
    this.initializeBackgroundHandler();
    this.loadStoredNotifications();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Set navigation reference (similar to Ionic navRef)
  public setNavigationRef(navRef: any) {
    console.log('🔗 Setting navigation reference:', navRef ? 'available' : 'null');
    this.navigationRef = navRef;
    // Don't clear pending notification even if nav ref becomes null
    // This ensures we can still handle pending notifications when app relaunches
    if (!navRef) {
      console.log('⚠️ Navigation reference cleared, but keeping pending notification');
      // this.pendingNotification = null; // Don't clear this
    }
  }

  // Similar to Ionic's checkNotfyMessage method
  private checkNotfyMessage = (type: string): boolean => {
    let trueMessage = false;
    NOTIFICATION_TYPE.forEach((element: string) => {
      if (type === element && element !== 'chat') trueMessage = true;
    });
    return trueMessage;
  };

  // Public method to handle initial notification when app is opened from quit state
  public handleInitialNotification(remoteMessage: any) {
    this.performNavigation(remoteMessage);
  }

  // Public method to handle notifications when app is foreground but service not initialized
  public handleForegroundNotification(remoteMessage: any) {
    this.performNavigation(remoteMessage);
  }

  // Get pending notification for debugging
  public getPendingNotification(): FirebaseMessagingTypes.RemoteMessage | null {
    return this.pendingNotification;
  }

  public storePendingNotification(remoteMessage: FirebaseMessagingTypes.RemoteMessage) {
    this.pendingNotification = remoteMessage;
  }

  private initializeBackgroundHandler() {
    // Handle background messages
    messaging().setBackgroundMessageHandler(
      async (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('📱 Background message received:', remoteMessage);
        await this.handleBackgroundMessage(remoteMessage);
      },
    );

    // Handle notification opened app (when app is in background/quit)
    messaging().onNotificationOpenedApp(
      (remoteMessage: FirebaseMessagingTypes.RemoteMessage) => {
        console.log('📱 Notification opened app from background:', remoteMessage);
        this.handleNotificationPress(remoteMessage);
        console.log("working1")
      },
    );

    // Handle notification opened app (when app was completely quit)
    messaging()
      .getInitialNotification()
      .then((remoteMessage: FirebaseMessagingTypes.RemoteMessage | null) => {
        if (remoteMessage) {
          console.log('📱 Notification opened app from quit state:', remoteMessage);
          this.handleNotificationPress(remoteMessage);
          console.log("working1")
        }
      });
  }

  private async handleBackgroundMessage(
    remoteMessage: FirebaseMessagingTypes.RemoteMessage,
  ) {
    try {
      if (remoteMessage.notification) {
        const notificationData: NotificationData = {
          id: remoteMessage.messageId || Date.now().toString(),
          title: remoteMessage.notification.title || 'Customer App',
          body: remoteMessage.notification.body || 'You have a new message',
          data: remoteMessage.data,
          timestamp: Date.now(),
          read: false,
        };

        await this.storeNotification(notificationData);
      }
    } catch (error) {
      console.error('Error handling background message:', error);
    }
  }

  private handleNotificationPress(
    remoteMessage: FirebaseMessagingTypes.RemoteMessage,
  ) {
    // Add delay to ensure navigation stack is ready
    setTimeout(() => {
      this.performNavigation(remoteMessage);
    }, 1000);
  }

  private performNavigation(remoteMessage: FirebaseMessagingTypes.RemoteMessage) {
    try {
      console.log('🔔 Background notification pressed:', remoteMessage);
      
      // Extract notification data more robustly
      const notificationType = this.extractNotificationType(remoteMessage);
      const notificationParams = this.extractNotificationParams(remoteMessage);
      
      console.log('📱 Notification data extracted:', {
        type: notificationType,
        params: notificationParams,
        title: remoteMessage.notification?.title,
        body: remoteMessage.notification?.body
      });

      // Wait for navigation to be ready
      setTimeout(() => {
        // Smart notification routing based on type
        if (notificationType === 'chat' || notificationType === 'message') {
          console.log('💬 Chat notification - redirecting to chat list');
          try {
            navigate(RouteNames.MCX_NAV_CHAT_LIST);
          } catch (navErr) {
            console.error('Error navigating to chat list, fallback to dashboard:', navErr);
            this.navigateToMessages();
          }
        } else {
          console.log('📧 Non-chat notification (type:', notificationType, ') - redirecting to messages screen');
          // All other notification types go to messages screen in dashboard
          this.navigateToMessages();
        }
      }, 1500); // Increased delay to ensure navigation is ready

      // Mark notification as read
      if (remoteMessage.messageId) {
        this.markAsRead(remoteMessage.messageId);
      }
    } catch (error) {
      console.error('❌ Error in notification navigation:', error);
      this.navigateToDashboard();
    }
  }

  // Update extractNotificationParams to handle more data formats
  private extractNotificationParams(remoteMessage: FirebaseMessagingTypes.RemoteMessage): string {
    const msg = remoteMessage as any;
    
    // Try all possible parameter locations
    const params = 
      msg.data?.params ||
      msg.params ||
      msg.data?.chatId ||
      msg.data?.appointmentId ||
      msg.data?.requestId ||
      msg.chatId ||
      msg.appointmentId ||
      msg.requestId ||
      '';

    // If params is an object, try to extract the relevant ID
    if (typeof params === 'object' && params !== null) {
      return params.id || params.chatId || params.appointmentId || params.requestId || '';
    }

    return params.toString();
  }

  // Helper to extract notification type safely from different payload shapes
  private extractNotificationType(remoteMessage: FirebaseMessagingTypes.RemoteMessage): string {
    const msg: any = remoteMessage as any;
    const type =
      msg.data?.type ||
      msg.type ||
      msg.data?.notificationType ||
      msg.notification?.tag ||
      '';
    return (type || '').toString();
  }
  private navigateToDashboard() {
    try {
      navigate(RouteNames.MCX_NAV_DashBoard);
    } catch (error) {
      console.error('Error navigating to dashboard:', error);
    }
  }

  private navigateToMessages = () => {
    try {
      navigate(RouteNames.MCX_NAV_DashBoard, {
        screen: RouteNames.MCX_NAV_MESSAGES,
        params: { tab: 2, isFromNotification: true }
      });
    } catch (error) {
      console.error('Error navigating to messages:', error);
      // Fallback to dashboard
      this.navigateToDashboard();
    }
  }

  private async loadStoredNotifications() {
    try {
      const stored = await AsyncStorage.getItem('notifications');
      if (stored) {
        this.notifications = JSON.parse(stored);
      }
    } catch (error) {
      console.error('Error loading stored notifications:', error);
    }
  }

  private async storeNotification(notification: NotificationData) {
    try {
      this.notifications.unshift(notification);
      // Keep only last 50 notifications
      this.notifications = this.notifications.slice(0, 50);
      await AsyncStorage.setItem(
        'notifications',
        JSON.stringify(this.notifications),
      );
    } catch (error) {
      console.error('Error storing notification:', error);
    }
  }

  public async markAsRead(notificationId: string) {
    try {
      const index = this.notifications.findIndex(n => n.id === notificationId);
      if (index !== -1) {
        this.notifications[index].read = true;
        await AsyncStorage.setItem(
          'notifications',
          JSON.stringify(this.notifications),
        );
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  public async markAllAsRead() {
    try {
      this.notifications = this.notifications.map(n => ({...n, read: true}));
      await AsyncStorage.setItem(
        'notifications',
        JSON.stringify(this.notifications),
      );
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  public getNotifications(): NotificationData[] {
    return this.notifications;
  }

  public getUnreadCount(): number {
    return this.notifications.filter(n => !n.read).length;
  }

  public async clearAllNotifications() {
    try {
      this.notifications = [];
      await AsyncStorage.removeItem('notifications');
    } catch (error) {
      console.error('Error clearing notifications:', error);
    }
  }

  // Send a test notification (for development)
  public async sendTestNotification() {
    try {
      const token = await messaging().getToken();
      console.log('FCM Token for testing:', token);

      Alert.alert(
        'Test Notification',
        `Copy this token to send test notifications:\n\n${token}`,
        [
          {
            text: 'Copy Token',
            onPress: () => {
              // In a real app, you'd use Clipboard API
              console.log('Token copied to console');
            },
          },
          {text: 'OK'},
        ],
      );
    } catch (error) {
      console.error('Error getting token for test:', error);
    }
  }
}

export default NotificationService;
