 import {getApp} from '@react-native-firebase/app';
 import {getAuth} from '@react-native-firebase/auth';
 import {getDatabase} from '@react-native-firebase/database';

 /**
  * AuthService: Helper APIs mirroring legacy Ionic flow
  * - isEmailExistInMechanic(email)
  * - isEmailExistInCustomer(email)
  * - resetPassword(email)
  */
 export type EmailExistFn = (email: string) => Promise<boolean>;

 const getDbRefByPath = (path: string) => {
   const app = getApp();
   const database = getDatabase(app);
   return database.ref(path);
 };

 export const isEmailExistInMechanic: EmailExistFn = async email => {
   const snapshot = await getDbRefByPath('mechanic')
     .orderByChild('email')
     .equalTo(email)
     .once('value');
   return snapshot.exists();
 };

 export const isEmailExistInCustomer: EmailExistFn = async email => {
   const snapshot = await getDbRefByPath('customer')
     .orderByChild('email')
     .equalTo(email)
     .once('value');
   return snapshot.exists();
 };

 export const resetPassword = async (email: string) => {
   const app = getApp();
   const auth = getAuth(app);
   await auth.sendPasswordResetEmail(email);
 };

 const AuthService = {
   isEmailExistInMechanic,
   isEmailExistInCustomer,
   resetPassword,
 };

 export default AuthService;
