import React from 'react';
import {createDrawerNavigator} from '@react-navigation/drawer';
import {View, StyleSheet, Platform} from 'react-native';
import {SafeAreaView} from 'react-native-safe-area-context';
import {useNavigation} from '@react-navigation/native';
import BottomTabNavigator from './BottomTabNavigator';
import {RouteNames} from '../utils/constants/AppStrings';
import CustomDrawerContent from '../components/navigation/CustomDrawerContent';
import UserProfile from '../screens/features/users/UserProfile';
import Schedule from '../screens/features/schedule/Schedule';
import SettingsScreen from '../screens/features/settings/SettingsScreen';
import MessagesScreen from '../screens/features/notifications/messages/MessagesScreen';
import FindMechanics from '../screens/features/find_mechanics/FindMechanics';
import Help from '../screens/help/Help';
import ReferFriend from '../screens/features/referral/ReferFriend';
import ServiceHistory from '../screens/features/History/ServiceHistory';
import AppBar from '../components/common/AppBar';
import ChatListScreen from '../screens/features/chat/chatList';
import ChatRoomScreen from '../screens/features/chat/ChatRoomScreen';
import MessageDetails from '../screens/features/notifications/messages/MessageDetails';
import { Colors } from '../utils/constants/Theme';
import EditProfile from '../screens/features/users/EditProfile';

const Drawer = createDrawerNavigator();

// Create a custom screen container with fixed AppBar
const ScreenWithFixedAppBar = ({children}: {children: React.ReactNode}) => {
  const navigation = useNavigation();

  const handleMailPress = () => {
    navigation.navigate(RouteNames.MCX_NAV_MESSAGES as never);
  };

  return (
    <SafeAreaView style={{flex: 1, backgroundColor: Colors.APPBAR_BG_COLOR}} edges={Platform.OS === 'ios' ? ['top'] : ['top', 'bottom']}>
      <View style={styles.appBarContainer}>
        <AppBar onMailPress={handleMailPress}/>
      </View>
      <View style={styles.contentContainer}>{children}</View>
    </SafeAreaView>
  );
};

// Wrap each screen with the fixed AppBar
const DashboardWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <BottomTabNavigator />
  </ScreenWithFixedAppBar>
);

const ProfileWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <UserProfile />
  </ScreenWithFixedAppBar>
);

const MessagesWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <MessagesScreen />
  </ScreenWithFixedAppBar>
);

const PendingWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <MessagesScreen />
  </ScreenWithFixedAppBar>
);

const FindMechanicWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <FindMechanics />
  </ScreenWithFixedAppBar>
);

const ScheduleWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <Schedule />
  </ScreenWithFixedAppBar>
);

const SettingsWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <SettingsScreen />
  </ScreenWithFixedAppBar>
);

const HelpWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <Help />
  </ScreenWithFixedAppBar>
);

const ReferFriendWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <ReferFriend />
  </ScreenWithFixedAppBar>
);

const ServiceHistoryWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <ServiceHistory />
  </ScreenWithFixedAppBar>
);
const ChatWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <ChatListScreen />
  </ScreenWithFixedAppBar>
);
const ChatRoomWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <ChatRoomScreen />
  </ScreenWithFixedAppBar>
);
const MessageDetailsWithAppBar = () => (
  <ScreenWithFixedAppBar>
    <MessageDetails />
  </ScreenWithFixedAppBar>
);

const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      drawerContent={props => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          backgroundColor: '#1e2a36',
          width: 320,
          // Set top margin to match AppBar height
        },
        drawerType: 'front',
        drawerPosition: 'right',
        overlayColor: 'rgba(0,0,0,0.7)',
      }}>
      <Drawer.Screen
        name="MainTabs"
        component={DashboardWithAppBar}
        options={{title: 'Dashboard'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_USER_PROFILE}
        component={ProfileWithAppBar}
        options={{title: 'Profile'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_MESSAGES}
        component={MessagesWithAppBar}
        options={{title: 'Messages'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_PENDING}
        component={PendingWithAppBar}
        initialParams={{ initialTab: 3 }}
        options={{title: 'Pending Tasks'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_FIND_MECHANIC}
        component={FindMechanicWithAppBar}
        options={{title: 'Find Mechanic'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_SCHEDULE}
        component={ScheduleWithAppBar}
        options={{title: 'Schedule Service'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_SETTINGS}
        component={SettingsWithAppBar}
        options={{title: 'Settings'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_HELP}
        component={HelpWithAppBar}
        options={{title: 'Help'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_REFER_FRIEND}
        component={ReferFriendWithAppBar}
        options={{title: 'Refer a Friend'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_WORK_HISTORY}
        component={ServiceHistoryWithAppBar}
        options={{title: 'Work History'}}
      />
      <Drawer.Screen
        name={RouteNames.MCX_NAV_CHAT_LIST}
        component={ChatWithAppBar}
        options={{title: 'Chats'}}
      />
       <Drawer.Screen
        name={RouteNames.MCX_NAV_CHAT_ROOM}
        component={ChatRoomWithAppBar}
        options={{title: 'Chats'}}
      />
       <Drawer.Screen
        name={RouteNames.MCX_NAV_MESSAGE_DETAILS}
        component={MessageDetailsWithAppBar}
        options={{title: 'Chats'}}
      />
    </Drawer.Navigator>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
  },
  appBarContainer: {
    width: '100%',
    backgroundColor: '#232e38',
    zIndex: 1,
  },
});

export default DrawerNavigator;
