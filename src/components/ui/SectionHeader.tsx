import React from 'react';
import {View, Text, StyleSheet} from 'react-native';
import {THEME} from '../../utils/constants/Theme';

type SectionHeaderProps = {
  title: string;
  headerTextColor?: string;
};

const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  headerTextColor = 'white',
}) => {
  return (
    <View style={styles(headerTextColor).container}>
      <Text style={styles(headerTextColor).title}>{title}</Text>
    </View>
  );
};

const styles = (headerTextColor: string) =>
  StyleSheet.create({
    container: {
      backgroundColor: '#000',
      padding: 10,
      borderBottomWidth: 1.8,
      borderBottomColor: THEME.COLORS.BORDER_SECONDARY,
    },
    title: {
      color: headerTextColor,
      fontSize: 16,
      fontWeight: 'bold',
    },
  });

export default SectionHeader;
