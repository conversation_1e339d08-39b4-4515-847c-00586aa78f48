import React from 'react';
import {View, Text, StyleSheet, TouchableOpacity, Image} from 'react-native';
import {THEME} from '../../utils/constants/Theme';
import {AppCommonIcons} from '../../utils/constants/AppStrings';

type PageHeaderProps = {
  title: string;
  onBack?: () => void;
};

export const PageHeader: React.FC<PageHeaderProps> = ({title, onBack}) => {
  return (
    <View style={styles.headerContainer}>
      {onBack && (
        <TouchableOpacity style={styles.backButton} onPress={onBack}>
          <Image
            source={AppCommonIcons.MCX_ARROW_RIGHT}
            style={styles.backIcon}
          />
        </TouchableOpacity>
      )}
      <Text style={styles.headerText}>{title}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  headerContainer: {
    width: '100%',
    backgroundColor: THEME.COLORS.PRIMARY_RED,
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
  },
  backButton: {
    padding: 8,
    borderRadius: 8,
    position: 'absolute',
    left: 10,
    zIndex: 1,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#FFFFFF',
    transform: [{rotate: '180deg'}],
  },
  headerText: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    textTransform: 'uppercase',
    flex: 1,
    textAlign: 'center',
  },
});