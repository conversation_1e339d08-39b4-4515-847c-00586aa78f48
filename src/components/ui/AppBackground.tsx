import {Image, StyleSheet} from 'react-native';
import {ICONS} from '../../utils/constants/Theme';

const AppBackground = () => {
  return <Image source={ICONS.TAB_BACKGROUND} style={styles.backgroundImage} />;
};

const styles = StyleSheet.create({
  backgroundImage: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    width: '100%',
    height: '100%',
    zIndex: -1,
  },
});

export default AppBackground;
