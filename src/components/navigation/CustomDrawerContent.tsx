import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  SafeAreaView,
  FlatList,
  Platform,
} from 'react-native';
import {DrawerContentComponentProps} from '@react-navigation/drawer';
import {useSafeAreaInsets} from 'react-native-safe-area-context';
import {menuDrawerData} from '../../utils/templates/TemplateConfig';
import {RouteNames} from '../../utils/constants/AppStrings';
import CustomButton from '../common/CustomButton';
import {Colors, Fonts, Sizes} from '../../utils/constants/Theme';
import {AppCommonIcons, AppStrings} from '../../utils/constants/AppStrings';
import {useAuth} from '../../utils/configs/AuthContext';
// Using centralized unread counter; no direct DB imports here
import {subscribeUnreadMessageCount} from '../../utils/services/UnreadMessageCounter';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {GC_SIGNUP_PROVIDER_TYPE_PREF} from '../../utils/globals';

const CustomDrawerContent: React.FC<DrawerContentComponentProps> = props => {
  const {navigation} = props;
  const {signOut} = useAuth();
  const insets = useSafeAreaInsets();

  const [messageBadgeCount, setMessageBadgeCount] = useState<number>(0);
  const unreadUnsubRef = useRef<() => void | undefined>(undefined);

  // Recompute unread service + unread system for Messages badge (shared service)
  useEffect(() => {
    unreadUnsubRef.current = subscribeUnreadMessageCount((count: number) => {
      setMessageBadgeCount(count);
    });
    return () => {
      if (unreadUnsubRef.current) {
        unreadUnsubRef.current();
      }
    };
  }, []);

  const handleLogout = async () => {
    const providerType = await AsyncStorage.getItem(
      GC_SIGNUP_PROVIDER_TYPE_PREF,
    );
    const isSocial = providerType && providerType !== 'normal';
    await signOut().then(() => {
      if (!isSocial) {
        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [{name: RouteNames.MCX_NAV_AppLoginPageScreen}],
          });
        }, 100);
      }
    });
  };

  const renderMenuItem = ({item, index}: {item: any; index: number}) => (
    <React.Fragment>
      <TouchableOpacity
        style={styles.menuItem}
        onPress={() => {
          if (item.id === 'find_mechanic') {
            navigation.navigate('MainTabs', {
              screen: RouteNames.MCX_FIND_MECHANIC,
            } as never);
          } else if (item.id === 'schedule_service') {
            navigation.navigate('MainTabs', {
              screen: RouteNames.MCX_SCHEDULE,
            } as never);
          } else if (item.id === 'home') {
            navigation.navigate('MainTabs', {
              screen: RouteNames.MCX_DASHBOARD,
            } as never);
          } else {
            const routeMap: {[key: string]: string} = {
              profile: RouteNames.MCX_NAV_USER_PROFILE,
              messages: RouteNames.MCX_NAV_MESSAGES,
              settings: RouteNames.MCX_NAV_SETTINGS,
              help: RouteNames.MCX_NAV_HELP,
              refer_friend: RouteNames.MCX_NAV_REFER_FRIEND,
              account_registration: RouteNames.MCX_NAV_ACCOUNT_REGISTRATION,
              work_history: RouteNames.MCX_NAV_WORK_HISTORY,
            };

            const screenName = routeMap[item.id];
            if (screenName) {
              navigation.navigate(screenName as never);
            }
          }
        }}
        activeOpacity={0.7}>
        <View style={styles.iconContainer}>
          <Image source={item.icon} style={styles.menuIcon} />
          {item.id === 'messages' && messageBadgeCount > 0 && (
            <View style={styles.iconBadge}>
              <Text style={styles.iconBadgeText}>
                {messageBadgeCount > 99 ? '99+' : messageBadgeCount}
              </Text>
            </View>
          )}
        </View>
        <Text style={styles.menuText}>{item.label}</Text>
      </TouchableOpacity>
      {/* Divider after each item except the last */}
      {index < menuDrawerData.length - 1 && <View style={styles.menuDivider} />}
    </React.Fragment>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Custom Header */}
      <View style={[styles.header, {paddingTop: Platform.OS === 'ios' ? 0 : insets.top}]}>
        <Image
          source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
          style={styles.logo}
          resizeMode="contain"
        />
      </View>
      {/* FlatList for menu items */}
      <FlatList
        data={menuDrawerData}
        renderItem={renderMenuItem}
        keyExtractor={item => item.id}
        showsVerticalScrollIndicator={false}
        ListFooterComponent={
          <View style={styles.logoutButtonContainer}>
            <CustomButton
              text={AppStrings.MCX_LOGOUT_TEXT}
              onPress={handleLogout}
              variant="outline"
              size="medium"
              backgroundColor="transparent"
              textColor="#fff"
              borderColor="#fff"
              borderWidth={1}
              style={styles.logoutButton}
              textStyle={styles.logoutText}
              isBoldText={true}
            />
          </View>
        }
      />
      {/* Version text at the very bottom */}
      <View style={[styles.versionContainer, {paddingBottom: insets.bottom}]}>
        <Text style={styles.versionText}>{AppStrings.MCX_VERSION}</Text>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1e2a36',
  },
  drawerContent: {
    paddingTop: 0,
    width: '100%',
    backgroundColor: 'red',
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 0,
    backgroundColor: 'transparent',
  },
  iconContainer: {
    width: 60,
    height: 55,
    backgroundColor: Colors.COMMON_DRAWER_ICON_CONTAINER_BG_COLOR,
    alignItems: 'center',
    justifyContent: 'center',
  },
  menuIcon: {
    width: 32,
    height: 32,
    tintColor: '#e74c3c',
  },
  iconBadge: {
    position: 'absolute',
    top: -6,
    right: -8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#E53E3E',
    borderWidth: 1,
    borderColor: '#1e2a36',
    zIndex: 2,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconBadgeText: {
    color: '#fff',
    fontSize: 9,
    fontWeight: '800',
  },
  menuText: {
    color: '#a7b6c2',
    fontSize: Sizes.MEDIUM,
    fontWeight: '700',
    fontFamily: Fonts.ROBO_REGULAR,
    marginLeft: 16,
  },
  badgeContainer: {
    marginLeft: 'auto',
    marginRight: 16,
    backgroundColor: '#E53E3E',
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    paddingHorizontal: 4,
    alignItems: 'center',
    justifyContent: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: Sizes.SMALL,
    fontWeight: '800',
  },
  logoutButtonContainer: {
    paddingHorizontal: 55,
    marginTop: 16,
    marginBottom: 60,
  },
  logoutButton: {
    borderRadius: 30,
    paddingVertical: 12,
    width: '100%',
    marginTop: 22,
  },
  logoutText: {
    color: '#fff',
    fontSize: Sizes.XLARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '700',
  },
  versionContainer: {
    position: 'absolute',
    bottom: 4,
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    borderTopWidth: 1,
    borderTopColor: Colors.SECONDARY,
    opacity: 0.7,
    paddingVertical: 14,
    backgroundColor: '#1e2a36',
  },
  versionText: {
    color: Colors.COMMON_WHITE_SHADE,
    fontSize: Sizes.SMALL,
    textAlign: 'center',
    fontWeight: '800',
  },
  header: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.COMMON_DRAWER_HEADER_TITLE_BG_COLOR,
    paddingVertical: 12,
  },
  logo: {
    width: 160,
    height: 40,
  },
  menuDivider: {
    height: 1,
    backgroundColor: 'rgba(255,255,255,0.08)',
    marginLeft: 0,
    marginRight: 0,
  },
});

export default CustomDrawerContent;
