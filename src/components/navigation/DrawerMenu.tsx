import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {menuDrawerData} from '../../utils/templates/TemplateConfig';
import {AppStrings} from '../../utils/constants/AppStrings';
import {useNavigation} from '@react-navigation/native';
import {RouteNames} from '../../utils/constants/AppStrings';
import {StackNavigationProp} from '@react-navigation/stack';

interface DrawerMenuProps {
  onClose: () => void;
}
type RootStackParamList = {
  [RouteNames.MCX_NAV_USER_PROFILE]: undefined;
  [RouteNames.MCX_NAV_MESSAGES]: undefined;
  [RouteNames.MCX_FIND_MECHANIC]: undefined;
  [RouteNames.MCX_SCHEDULE]: undefined;
  [RouteNames.MCX_NAV_SETTINGS]: undefined;
  [RouteNames.MCX_NAV_HELP]: undefined;
  [RouteNames.MCX_NAV_REFER_FRIEND]: undefined;
  [RouteNames.MCX_NAV_PRIVACY_POLICY]: undefined;
  [RouteNames.MCX_NAV_TERMS_CONDITIONS]: undefined;
  LoginMainScreen: undefined;
  [RouteNames.MCX_NAV_WORK_HISTORY]: undefined;
};
type NavigationProp = StackNavigationProp<RootStackParamList>;

const DrawerMenu: React.FC<DrawerMenuProps> = ({onClose}) => {
  const navigation = useNavigation<NavigationProp>();

  const handleMenuItemPress = (id: string) => {
    // Map menu item IDs to screen names
    const screenMap: {[key: string]: keyof RootStackParamList} = {
      profile: RouteNames.MCX_NAV_USER_PROFILE,
      messages: RouteNames.MCX_NAV_MESSAGES,
      find_mechanic: RouteNames.MCX_FIND_MECHANIC,
      work_history: RouteNames.MCX_NAV_WORK_HISTORY,
      schedule_service: RouteNames.MCX_SCHEDULE,
      settings: RouteNames.MCX_NAV_SETTINGS,
      help: RouteNames.MCX_NAV_HELP,
      refer_friend: RouteNames.MCX_NAV_REFER_FRIEND,
    };

    const screenName = screenMap[id];
    if (screenName) {
      (navigation as any).navigate(screenName);
      onClose();
    }
  };

  const handleLogout = () => {
    //navigation.navigate(RouteNames.MCX_NAV_LoginMainScreen as never);
    onClose();
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.menuItems}>
        {menuDrawerData.map(item => (
          <TouchableOpacity
            key={item.id}
            style={styles.menuItem}
            onPress={() => handleMenuItemPress(item.id)}>
            <Image source={item.icon} style={styles.menuIcon} />
            <Text style={styles.menuText}>{item.label}</Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      <View style={styles.footer}>
        <Text style={styles.versionText}>{AppStrings.MCX_VERSION}</Text>
        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutText}>{AppStrings.MCX_LOGOUT_TEXT}</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1e2a36',
  },
  menuItems: {
    flex: 1,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderBottomWidth: 0.5,
    borderBottomColor: 'rgba(255,255,255,0.1)',
  },
  menuIcon: {
    width: 24,
    height: 24,
    marginRight: 16,
    tintColor: '#e74c3c',
  },
  menuText: {
    color: '#a7b6c2',
    fontSize: 16,
  },
  footer: {
    padding: 20,
    alignItems: 'center',
    borderTopWidth: 0.5,
    borderTopColor: 'rgba(255,255,255,0.1)',
  },
  versionText: {
    color: '#a7b6c2',
    fontSize: 12,
    marginBottom: 10,
  },
  logoutButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#fff',
    borderRadius: 25,
    paddingVertical: 10,
    paddingHorizontal: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default DrawerMenu;
