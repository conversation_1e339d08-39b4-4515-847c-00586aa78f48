import React from 'react';
import { View, ScrollView, StyleSheet, ImageBackground, ViewStyle } from 'react-native';
import { Colors, LayoutConstants } from '../../utils/constants/Theme';
import { AppCommonIcons } from '../../utils/constants/AppStrings';

interface ScreenLayoutProps {
    children: React.ReactNode;
    useScrollView?: boolean;
    useImageBackground?: boolean;
    centerContent?: boolean;
    backgroundColor?: string;
    contentContainerStyle?: ViewStyle;
    scrollContainerStyle?: ViewStyle;
    topPadding?: number;
    useHorizontalPadding?: boolean;
    keyboardShouldPersistTaps?: 'always' | 'never' | 'handled';
    fixedBottomContent?: React.ReactNode;
    fixedBottomStyle?: ViewStyle;
}

const ScreenLayout = React.forwardRef<ScrollView, ScreenLayoutProps>(({
    children,
    useScrollView = true,
    useImageBackground = true,
    centerContent = true,
    backgroundColor = 'transparent',
    contentContainerStyle,
    scrollContainerStyle,
    topPadding,
    useHorizontalPadding = false,
    keyboardShouldPersistTaps,
    fixedBottomContent,
    fixedBottomStyle,
}, ref) => {
    const renderContent = () => {
        if (useScrollView) {
            return (
                <ScrollView
                    ref={ref}
                    contentContainerStyle={[
                        styles.scrollContainer,
                        centerContent && styles.centeredContent,
                        topPadding !== undefined && { paddingTop: topPadding },
                        useHorizontalPadding && { paddingHorizontal: LayoutConstants.SCREEN_HORIZONTAL_PADDING },
                        scrollContainerStyle,
                    ]}
                    showsVerticalScrollIndicator={false}
                    keyboardShouldPersistTaps={keyboardShouldPersistTaps}
                >
                    {children}
                </ScrollView>
            );
        }

        return (
            <View style={[
                useHorizontalPadding ? styles.contentContainerWithPadding : styles.contentContainer,
                topPadding !== undefined && { paddingTop: topPadding },
                contentContainerStyle,
            ]}>
                {children}
            </View>
        );
    };
    const containerContent = (
        <View style={[styles.container, { backgroundColor }]}>
            {renderContent()}
            {fixedBottomContent && (
                <View style={[styles.fixedBottomSection, fixedBottomStyle]}>
                    {fixedBottomContent}
                </View>
            )}
        </View>
    );

    if (useImageBackground) {
        return (
            <ImageBackground
                source={AppCommonIcons.MCX_BACKGROUND_IMAGE}
                style={styles.backgroundImage}
                resizeMode="cover"
            >
                {containerContent}
            </ImageBackground>
        );
    }

    return containerContent;
});

ScreenLayout.displayName = 'ScreenLayout';

const styles = StyleSheet.create({
    backgroundImage: {
        flex: 1,
        width: '100%',
        height: '100%',
    },
    container: {
        flex: 1,
    },
    contentContainer: {
        flex: 1,
    },
    contentContainerWithPadding: {
        flex: 1,
        paddingHorizontal: LayoutConstants.SCREEN_HORIZONTAL_PADDING,
    },
    scrollContainer: {
        flexGrow: 1,
        paddingBottom: LayoutConstants.SCROLL_BOTTOM_PADDING,
    },
    centeredContent: {
        alignItems: 'center',
    },
    fixedBottomSection: {
        position: 'absolute',
        bottom: 0,
        left: LayoutConstants.HORIZONTAL_GAP_PERCENTAGE,
        right: LayoutConstants.HORIZONTAL_GAP_PERCENTAGE,
        width: LayoutConstants.CONTENT_WIDTH,
        backgroundColor: Colors.COMMON_WHITE_SHADE,
        paddingHorizontal: 16,
        paddingVertical: 8,
    },
});

export default ScreenLayout;
