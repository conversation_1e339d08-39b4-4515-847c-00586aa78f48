import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';
import { AppCommonIcons } from '../../utils/constants/AppStrings';
import { matchCardIcon } from '../../utils/cardUtils/cardUtils';

// Interface for PaymentCardComponent props
interface PaymentCardProps {
  card: {
    type: string;
    maskedNumber: string;
    brandShort: string;
  };
  onDelete: () => void;
}

// Reusable Payment Card Component
const PaymentCardComponent = ({ card, onDelete }: PaymentCardProps) => {
  // Get the appropriate card icon based on brand
  const cardIcon = matchCardIcon(card.brandShort);

  return (
    <View style={styles.paymentCardRow}>
      <View style={styles.cardContent}>
        <Image
          source={cardIcon}
          style={styles.cardIcon}
          resizeMode="contain"
        />
        <Text style={styles.cardNumber}>{card.maskedNumber}</Text>
      </View>
      <TouchableOpacity style={styles.deleteIconContainer} onPress={onDelete}>
        <Image
          source={AppCommonIcons.MCX_DELETE_ICON}
          style={styles.deleteIcon}
          resizeMode="contain"
        />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  // Payment Card Styles
  paymentCardRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cardIcon: {
    width: 40,
    height: 25,
    marginRight: 12,
  },
  cardNumber: {
    fontSize: Sizes.LARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
  },
  deleteIconContainer: {
    padding: 8,
  },
  deleteIcon: {
    width: 20,
    height: 20,
    tintColor: Colors.COMMON_BlACK_SHADE,
  },
});

export default PaymentCardComponent;