import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Image } from 'react-native';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';
import HorizontalDivider from '../common/HorizontalDivider';

interface CustomCardListStyleProps {
    id?: string;
    title: string;
    subtitle?: string;
    rightText?: string;
    rightTextColor?: string;
    rightTextSize?: number;
    icon?: object;
    cardBackgroundColor?: string;
    textColor?: string;
    subtitleColor?: string;
    isSelected?: boolean;
    onPress?: (id?: string) => void;
    showHorizontalLine?: boolean;
    isDefault?: boolean;
    horizontalLineColor?: string;
    horizontalLineSize?: number;
    rightElementType?: 'none' | 'radio' | 'checkbox' | 'image' | 'indicator' | 'text';
    rightIcon?: object;
    rightIconStyle?: object;
    radioButtonColor?: string;
    checkboxColor?: string;
    indicatorColor?: string;
}

const CustomCardListStyle = ({
    id,
    title,
    subtitle,
    rightText,
    rightTextColor = Colors.COMMON_GREY_SHADE_LIGHT,
    rightTextSize = Sizes.SMALL,
    icon,
    cardBackgroundColor = 'transparent',
    textColor = Colors.SECONDARY,
    subtitleColor = Colors.COMMON_GREY_SHADE_DARK,
    isSelected = false,
    onPress,
    showHorizontalLine = false,
    isDefault = false,
    horizontalLineColor = Colors.COMMON_GREY_SHADE_LIGHT,
    horizontalLineSize = 1,
    rightElementType = 'none',
    rightIcon,
    rightIconStyle = {},
    radioButtonColor = Colors.PRIMARY,
    checkboxColor = Colors.PRIMARY,
    indicatorColor = Colors.PRIMARY,
}: CustomCardListStyleProps) => {

    const renderRightElement = () => {
        switch (rightElementType) {
            case 'radio':
                return (
                    <View style={styles.rightElementContainer}>
                        <View style={[
                            styles.radioButton,
                            { borderColor: radioButtonColor },
                            isSelected && { backgroundColor: radioButtonColor }
                        ]}>
                            {isSelected && <View style={styles.radioButtonInner} />}
                        </View>
                    </View>
                );

            case 'checkbox':
                return (
                    <View style={styles.rightElementContainer}>
                        <View style={[
                            styles.checkbox,
                            { borderColor: checkboxColor },
                            isSelected && { backgroundColor: checkboxColor }
                        ]}>
                            {isSelected && (
                                <Text style={styles.checkmark}>✓</Text>
                            )}
                        </View>
                    </View>
                );

            case 'image':
                return (
                    <View style={styles.rightElementContainer}>
                        {rightText && (
                            <Text style={[styles.rightText, { color: rightTextColor, fontSize: rightTextSize }]}>
                                {rightText}
                            </Text>
                        )}
                        {rightIcon && (
                            <Image
                                source={rightIcon}
                                style={[styles.rightImage, rightIconStyle]}
                            />
                        )}
                    </View>
                );

            case 'text':
                return rightText ? (
                    <View style={styles.rightElementContainer}>
                        <Text style={[styles.rightText, { color: rightTextColor, fontSize: rightTextSize }]}>
                            {rightText}
                        </Text>
                    </View>
                ) : null;

            case 'indicator':
                return isSelected ? (
                    <View style={styles.rightElementContainer}>
                        <View style={[
                            styles.selectedIndicator,
                            { backgroundColor: indicatorColor }
                        ]} />
                    </View>
                ) : null;

            case 'none':
            default:
                return null;
        }
    };

    return (
       <View style={{ marginBottom: 10 }}>
            <TouchableOpacity
                style={[
                    styles.cardContainer,
                    { backgroundColor: cardBackgroundColor },
                    isSelected && rightElementType !== 'none' && styles.selectedCard
                ]}
                onPress={() => onPress && onPress(id)}
            >
                {/* Left Icon Section */}
                {icon && (
                    <View style={styles.iconContainer}>
                        <Image source={icon} style={styles.icon} />
                    </View>
                )}

                {/* Content Section */}
                <View style={styles.contentContainer}>
                    <Text style={[
                        styles.title,
                        { color: isDefault ? Colors.PRIMARY : textColor },
                        isDefault && styles.defaultTitle
                    ]}>
                        {title}
                        {isDefault && ' (Default)'}
                    </Text>
                    {subtitle && (
                        <Text style={[styles.subtitle, { color: subtitleColor }]}>
                            {subtitle}
                        </Text>
                    )}
                </View>

                {/* Right Element */}
                {renderRightElement()}
            </TouchableOpacity>

            {/* Horizontal Divider */}
            {showHorizontalLine && (
                <HorizontalDivider color={horizontalLineColor} height={horizontalLineSize || 1} />
            )}
        </View>
    );
};

const styles = StyleSheet.create({
    cardContainer: {
        paddingVertical: 10,
        paddingHorizontal: 12,
        width: '100%',
        borderRadius: 6,
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'transparent',
    },
    selectedCard: {
        backgroundColor: Colors.COMMON_WHITE_SHADE,
    },
    iconContainer: {
        marginRight: 12,
    },
    icon: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
    },
    contentContainer: {
        flex: 1,
    },
    title: {
        fontSize: Sizes.LARGE,
        fontFamily: Fonts.ROBO_REGULAR,
    },
    defaultTitle: {
        color: Colors.COMMON_GREY_SHADE_DARK,
    },
    subtitle: {
        fontSize: Sizes.MEDIUM,
        fontFamily: Fonts.ROBO_REGULAR,
        lineHeight: 16,
    },
    rightElementContainer: {
        marginLeft: 8,
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
    },

    // Radio button styles
    radioButton: {
        width: 20,
        height: 20,
        borderRadius: 10,
        borderWidth: 2,
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
    },
    radioButtonInner: {
        width: 8,
        height: 8,
        borderRadius: 4,
        backgroundColor: '#fff',
    },

    // Checkbox styles
    checkbox: {
        width: 20,
        height: 20,
        borderRadius: 3,
        borderWidth: 2,
        backgroundColor: 'transparent',
        justifyContent: 'center',
        alignItems: 'center',
    },
    checkmark: {
        color: '#fff',
        fontSize: 14,
        fontWeight: 'bold',
    },

    // Image styles
    rightImage: {
        width: 24,
        height: 24,
        resizeMode: 'contain',
    },

    // Right text styles
    rightText: {
        fontFamily: Fonts.ROBO_REGULAR,
        marginRight: 8,
    },

    // Indicator styles
    selectedIndicator: {
        width: 12,
        height: 12,
        borderRadius: 6,
    },
});

export default CustomCardListStyle;