import React, {useEffect} from 'react';
import {Alert, AppState} from 'react-native';
import useFcmToken from '../../hooks/useFcmToken';
import NotificationService from '../../services/NotificationService';
import {navigate, navigationRef} from '../../navigations/navigationRef';
import {RouteNames} from '../../utils/constants/AppStrings';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {NOTIFICATION_TYPE} from '../../utils/globals';
import messaging from '@react-native-firebase/messaging';

const FcmForeground: React.FC = () => {
  const {
    token: fcmToken,
    notificationPermissionStatus,
    messagePayload,
  } = useFcmToken();

  // Initialize NotificationService with navigation reference
  useEffect(() => {
    const notificationService = NotificationService.getInstance();
    notificationService.setNavigationRef(navigationRef);
  }, []);

  // Correct implementation matching user's Ionic code
  const checkNotfyMessage = (type: string): boolean => {
    let trueMessage = false;
    NOTIFICATION_TYPE.forEach((element: string) => {
      if (type === element && element !== 'chat') trueMessage = true;
    });
    return trueMessage;
  };

  // Enhanced navigation logic for foreground notifications
  const handleForegroundNotification = async (notificationData: any) => {
    // Handle both possible notification structures
    const type = notificationData.data?.type || notificationData.type;
    const params = notificationData.data?.params || notificationData.params;
    const title = notificationData.notification?.title || notificationData.title;
    const body = notificationData.notification?.body || notificationData.body;
    
    console.log('📱 Foreground notification received:', notificationData);

    try {
      Alert.alert(
        title || 'New Notification',
        body || 'You have a new message',
        [
          {text: 'Dismiss', style: 'cancel'},
          {
            text: 'View',
            onPress: () => {
              if (checkNotfyMessage(type)) {
                navigate(RouteNames.MCX_NAV_DashBoard, { screen: RouteNames.MCX_NAV_MESSAGES, params: { tab: 2 } });
              }
              // Handle both "chat" and "message" types for chat navigation
              if (type === "chat" || type === "message") {
                navigate(RouteNames.MCX_NAV_CHAT_LIST, { chatId: params });
              } 
              // else if (type === 'appointment' || type === 'workrequest') {
              //   // Navigate to appointment details
              //   navigate(RouteNames.MCX_NAV_AppointmentDetails, {
              //     'appointment-detail': {
              //       'work-request-id': params,
              //       'appointment-id': params,
              //     }
              //   });
              // }
            },
          },
        ],
      );
    } catch (error) {
      console.error('Error in foreground notification navigation:', error);
    }
  };

  // Handle app state changes to ensure background navigation works
  useEffect(() => {
    const handleAppStateChange = (nextAppState: string) => {
      if (nextAppState === 'active') {
        console.log('🟢 App became active - checking for pending notifications');
        
        // Check if app was opened from notification
        messaging()
          .getInitialNotification()
          .then((remoteMessage: any) => {
            if (remoteMessage) {
              console.log('📱 App opened from quit state via notification:', remoteMessage);
              // Add delay to ensure navigation stack is ready
              setTimeout(() => {
                const notificationService = NotificationService.getInstance();
                notificationService.handleInitialNotification(remoteMessage);
              }, 1500);
            }
          });
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription.remove();
    };
  }, []);

  // Handle foreground notifications
  useEffect(() => {
    if (messagePayload) {
      console.log('Received FCM message payload:', messagePayload);
      
      handleForegroundNotification(messagePayload);
    }
  }, [messagePayload]);

  // Initialize notification service and set in-app flag
  useEffect(() => {
    // Similar to localStorage.setItem(GC_SETTINGS_INAPPLICATION, 'true') in Ionic
    const setInAppFlag = async () => {
      try {
        await AsyncStorage.setItem('GC_SETTINGS_INAPPLICATION', 'true');
        console.log('✅ FCM initialized successfully with token:', fcmToken);
      } catch (error) {
        console.error('Error setting in-app flag:', error);
      }
    };

    if (notificationPermissionStatus === 'granted' && fcmToken) {
      setInAppFlag();
    }
  }, [fcmToken, notificationPermissionStatus]);

  // This component doesn't render anything visible
  return null;
};

export default FcmForeground;
