import React, {useEffect, useRef, useState} from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { AppCommonIcons } from '../../utils/constants/AppStrings';
import { Colors, CommonUIParams } from '../../utils/constants/Theme';
import { useNavigation, DrawerActions } from '@react-navigation/native';
import { RouteNames } from '../../utils/constants/AppStrings';
import { onValue } from '@react-native-firebase/database';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { GC_CUSTOMER_ID } from '../../utils/globals';
import { ChatService } from '../../utils/services/ChatService';
import { subscribeUnreadMessageCount } from '../../utils/services/UnreadMessageCounter';

interface AppBarProps {
  onMailPress?: () => void;
  onChatPress?: () => void;
  onMenuPress?: () => void;
  onBackPress?: () => void;
  // Icon visibility props
  showMailIcon?: boolean;
  showChatIcon?: boolean;
  showMenuIcon?: boolean;
  showBackButton?: boolean;
  showLogo?: boolean;
  // Custom title
  title?: string;
  titleColor?: string;
  // Badge counts (optional overrides)
  messageCount?: number;
  chatCount?: number;
}

const Badge = ({ count }: { count: number }) => (
  <View style={styles.badge}>
    <Text style={styles.badgeText}>
      {count}
    </Text>
  </View>
);

const AppBar: React.FC<AppBarProps> = ({
  onMailPress,
  onChatPress,
  onMenuPress,
  onBackPress,
  showMailIcon = true,
  showChatIcon = true,
  showMenuIcon = true,
  showBackButton = false,
  showLogo = true,
  title,
  titleColor = '#fff',
  messageCount = 0,
  chatCount = 0,
}) => {
  const navigation = useNavigation();
  const [mailBadge, setMailBadge] = useState<number>(0);
  const [chatBadge, setChatBadge] = useState<number>(0);

  const myChatsUnsub = useRef<null | (() => void)>(null);
  const chatLogUnsubs = useRef<Record<string, (() => void) | undefined>>({});
  const unreadUnsubRef = useRef<() => void | undefined>(undefined);

  useEffect(() => {
    let mounted = true;
    let customerId: string | null = null;
    // unread mail badge handled centrally

    (async () => {
      try {
        customerId = await AsyncStorage.getItem(GC_CUSTOMER_ID);
        unreadUnsubRef.current = subscribeUnreadMessageCount((count: number) => {
          if (mounted) setMailBadge(count);
        });
        // Exclude pending/multiple from unread mail badge by design

        // Chats unread
        if (customerId) {
          myChatsUnsub.current = onValue(ChatService.allChatsRef(customerId), listSnap => {
            const seenMap: Record<string, boolean> = {};
            // cleanup old chat log listeners
            Object.values(chatLogUnsubs.current).forEach(unsub => {
              if (typeof unsub === 'function') {
                try { unsub(); } catch {}
              }
            });
            chatLogUnsubs.current = {};
            listSnap.forEach(childSnap => {
              const roomKey = childSnap.key as string;
              chatLogUnsubs.current[roomKey] = onValue(ChatService.chatLogRef(roomKey), chatSnap => {
                const val = chatSnap.val() || {};
                const lastSender = val['last-sender'];
                const seen = val.seen === true;
                const isNew = customerId && lastSender !== customerId && !seen;
                seenMap[roomKey] = !!isNew;
                const total = Object.values(seenMap).filter(Boolean).length;
                if (mounted) {
                  setChatBadge(total);
                }
              });
              return undefined;
            });
          });
        }
      } catch {}
    })();

    return () => {
      mounted = false;
      try {
        if (unreadUnsubRef.current) unreadUnsubRef.current();
        if (typeof myChatsUnsub.current === 'function') {
          try { myChatsUnsub.current(); } catch {}
        }
        Object.values(chatLogUnsubs.current).forEach(unsub => {
          if (typeof unsub === 'function') {
            try { unsub(); } catch {}
          }
        });
      } catch {}
    };
  }, []);
  const handleMenuIconPress = () => {
    navigation.dispatch(DrawerActions.toggleDrawer());
    if (onMenuPress) {
      onMenuPress();
    }
  };

  const handleChatPress = () => {
    navigation.navigate(RouteNames.MCX_NAV_CHAT_LIST as never);
    if (onChatPress) {
      onChatPress();
    }
  };

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={styles.container}>
        {/* Left Section */}
        <View style={styles.leftSection}>
          {showBackButton && (
            <TouchableOpacity onPress={handleBackPress} style={styles.iconButton}>
              <Icon name="arrow-back" size={24} color="#fff" />
            </TouchableOpacity>
          )}
          {showMailIcon && !showBackButton && (
            <TouchableOpacity onPress={onMailPress} style={styles.iconButton}>
              <View>
                <Image source={AppCommonIcons.MCX_MESSAGE_ICON} style={styles.messageIcon} />
                {(messageCount || mailBadge) > 0 && (
                  <Badge count={messageCount || mailBadge} />
                )}
              </View>
            </TouchableOpacity>
          )}
        </View>

        {/* Center Section */}
        <View style={styles.titleContainer}>
          {showLogo && !title && (
            <Image
              source={AppCommonIcons.MCX_MYCANX_LOGO_TEXT}
              style={styles.logo}
              resizeMode="contain"
            />
          )}
          {title && (
            <Text style={[styles.titleText, { color: titleColor }]}>
              {title}
            </Text>
          )}
        </View>

        {/* Right Section */}
        <View style={styles.rightIcons}>
          {showChatIcon && (
            <TouchableOpacity onPress={handleChatPress} style={styles.iconButton}>
              <View>
                <Image source={AppCommonIcons.MCX_CHAT_ICON} style={styles.icon} />
                {(chatCount || chatBadge) > 0 && (
                  <Badge count={chatCount || chatBadge} />
                )}
              </View>
            </TouchableOpacity>
          )}
          {showMenuIcon && (
            <TouchableOpacity onPress={handleMenuIconPress} style={styles.iconButton}>
              <Image source={AppCommonIcons.MCX_MENU_ICON} style={styles.icon} />
            </TouchableOpacity>
          )}
        </View>
      </View>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    backgroundColor: Colors.APPBAR_BG_COLOR,
    zIndex: 10,
  },
  container: {
    height: Platform.OS === 'ios' ? 44 : CommonUIParams.CUSTOM_APP_BAR_TITLE_HEIGHT,
    backgroundColor: Colors.APPBAR_BG_COLOR,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 8,
  },
  iconButton: {
    padding: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 40,
    minHeight: 40,
  },
  icon: {
    width: 24,
    height: 24,
    tintColor: '#fff',
    resizeMode: 'contain',
  },
  messageIcon: {
    width: 24,
    height: 24,
    tintColor: '#fff',
    resizeMode: 'contain',
  },
  titleContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    height: 24,
    width: 120,
    alignSelf: 'center',
  },
  rightIcons: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    minWidth: 40,
  },
  titleText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  badge: {
    position: 'absolute',
    right: -2,
    top: -2,
    backgroundColor: 'red',
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
  },
});

export default AppBar;
