// RecordNotFound.js
import React from 'react';
import {View, Text, StyleSheet, Image} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const RecordNotFound = () => {
  return (
    <View style={recordNotFoundStyles.container}>
      <View style={recordNotFoundStyles.iconContainer}>
        <Icon name="search-off" size={60} color="#c41e3a" />
      </View>
      <Text style={recordNotFoundStyles.title}>SORRY</Text>
      <Text style={recordNotFoundStyles.subtitle}>NO DATA FOUND</Text>
    </View>
  );
};

const recordNotFoundStyles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
   // borderRadius: 8,
    padding: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#c41e3a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666',
  },
});

export default RecordNotFound;
