import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { CommonUIParams, Fonts, Sizes } from '../../utils/constants/Theme';
import { AppCommonIcons } from '../../utils/constants/AppStrings';

interface TitleSectionProps {
  title: string;
  bgColor: string;
  textColor: string;
  style?: object;
  textStyle?: object;
  onBack?: () => void; // Added for back button
}

const TitleSection: React.FC<TitleSectionProps> = ({
  title,
  bgColor,
  textColor,
  style,
  textStyle,
  onBack,
}) => (
  <View style={[styles.TitleSection, { backgroundColor: bgColor }, style]}>
    {onBack && (
      <TouchableOpacity style={styles.backButton} onPress={onBack}>
        <Image
          source={AppCommonIcons.MCX_ARROW_RIGHT}
          style={styles.backIcon}
        />
      </TouchableOpacity>
    )}
    <Text style={[styles.TitleSectionText, { color: textColor }, textStyle]}>
      {title}
    </Text>
    {/* Spacer for alignment if back button exists */}
    {onBack && <View style={{ width: 32 }} />}
  </View>
);

const styles = StyleSheet.create({
  TitleSection: {
    width: '100%',
    height: CommonUIParams.CUSTOM_SECTION_TITLE_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    flexDirection: 'row',
    position: 'relative',
  },
  TitleSectionText: {
    fontWeight: '600',
    fontSize: Sizes.XXLARGE,
    fontFamily: Fonts.ROBO_REGULAR,
    flex: 1,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  backButton: {
    position: 'absolute',
    left: 10,
    zIndex: 1,
    padding: 8,
    borderRadius: 8,
  },
  backIcon: {
    width: 24,
    height: 24,
    tintColor: '#fff',
    transform: [{ rotate: '180deg' }],
  },
});

export default TitleSection;