import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Colors, Sizes } from '../../utils/constants/Theme';

interface CustomTabProps {
  label: string;
  active: boolean;
  onPress: () => void;
  disabled?: boolean;
}

const CustomTab: React.FC<CustomTabProps> = ({ label, active, onPress, disabled = false }) => {
  return (
    <TouchableOpacity
      onPress={disabled ? undefined : onPress}
      disabled={disabled}
      style={[styles.tab, active ? styles.activeTab : styles.inactiveTab, disabled && styles.disabledTab]}>
      <Text style={[styles.tabText, active ? styles.activeText : styles.inactiveText, disabled && styles.disabledText]}>
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeTab: {
    backgroundColor: '#FFFFFF',
  },
  inactiveTab: {
    backgroundColor: Colors.COMMON_TAB_SECTION_BG_COLOR,
  },
  disabledTab: {
    opacity: 0.5,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  activeText: {
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    fontSize:Sizes.XLARGE,
  },
  inactiveText: {
    fontSize:Sizes.XLARGE,
    color: Colors.COMMON_GREY_SHADE_LIGHT, // Inactive text color
  },
  disabledText: {
    color: Colors.COMMON_GREY_SHADE_LIGHT, // Same color but with opacity from parent
  },
});

export default CustomTab;
