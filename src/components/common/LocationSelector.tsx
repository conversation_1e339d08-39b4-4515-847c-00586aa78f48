import React, { useState, useEffect, useCallback } from 'react';
import { View, Text, TouchableOpacity, Platform, Alert, StyleSheet } from 'react-native';
import Geolocation from 'react-native-geolocation-service';
import { Colors, Fonts, Sizes, CommonUIParams } from '../../utils/constants/Theme';
import { AppStrings } from '../../utils/constants/AppStrings';
import CommonDropdown from './CommonDropdown';
import { VehicleService } from '../../utils/services/VehicleService';
import { useAuth } from '../../utils/configs/AuthContext';
import axios from 'axios';
import Config from 'react-native-config';
import { request, check, PERMISSIONS, RESULTS } from 'react-native-permissions';

interface LocationData {
  address_array: {
    address1: string;
    address2: string;
    city: string;
    state: string;
    country: string;
    zipcode: string;
  };
  formatted_address: string;
  location: {
    latitude: number;
    longitude: number;
  };
}

interface SavedLocation {
  id: string;
  name: string;
  address: string;
  coordinates: {
    latitude: number;
    longitude: number;
  };
}

interface LocationSelectorProps {
  onLocationChange: (locationData: LocationData | null) => void;
  onLocationTypeChange?: (type: 'currentlocation' | 'savedlocation' | 'searchlocation') => void;
  initialLocationType?: 'currentlocation' | 'savedlocation' | 'searchlocation';
  showSavedLocations?: boolean;
  initialLocationData?: LocationData | null;
  disableRadioButtonsInitially?: boolean;
}

const LocationSelector: React.FC<LocationSelectorProps> = ({
  onLocationChange,
  onLocationTypeChange,
  initialLocationType = 'currentlocation',
  showSavedLocations = true,
  initialLocationData = null,
  disableRadioButtonsInitially = false,
}) => {
  const { user } = useAuth();
  const [locationType, setLocationType] = useState<'currentlocation' | 'savedlocation' | 'searchlocation'>(initialLocationType);
  const [customerRequestAddress, setCustomerRequestAddress] = useState<LocationData | null>(null);
  const [savedLocations, setSavedLocations] = useState<SavedLocation[]>([]);
  const [selectedSavedLocation, setSelectedSavedLocation] = useState('');
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [radioButtonsEnabled, setRadioButtonsEnabled] = useState(!disableRadioButtonsInitially);

  useEffect(() => {
    if (showSavedLocations) {
      fetchSavedLocations();
    }
  }, [showSavedLocations]);

  // Set initial location data if provided
  useEffect(() => {
    if (initialLocationData) {
      setCustomerRequestAddress(initialLocationData);
      // Don't call onLocationChange for initial data to avoid overwriting profile data
    }
  }, [initialLocationData]);

  // Auto-fetch current location on mount if it's the initial type and no initial data
  useEffect(() => {
    if (initialLocationType === 'currentlocation' && !initialLocationData) {
      requestCurrentLocation();
    }
  }, []);

  useEffect(() => {
    if (locationType === 'currentlocation' && !customerRequestAddress) {
      requestCurrentLocation();
    }
  }, [locationType]);

  useEffect(() => {
    if (selectedSavedLocation && savedLocations.length > 0) {
      const location = savedLocations.find(l => l.id === selectedSavedLocation);
      if (location) {
        const addressParts = location.address.split(',').map((part: string) => part.trim());
        const locationData: LocationData = {
          address_array: {
            address1: addressParts[0] || '',
            address2: '',
            city: addressParts[1] || '',
            state: addressParts[2] || '',
            country: addressParts[3] || 'India',
            zipcode: '',
          },
          formatted_address: location.address,
          location: location.coordinates,
        };
        setCustomerRequestAddress(locationData);
        onLocationChange(locationData);
      }
    }
  }, [selectedSavedLocation, savedLocations]);

  const fetchSavedLocations = useCallback(async () => {
    try {
      if (user?.uid) {
        const locations = await VehicleService.fetchSavedLocations(user.uid);
        setSavedLocations(locations);
      }
    } catch (error) {
      console.error('Error fetching saved locations:', error);
      setSavedLocations([]);
    }
  }, [user?.uid]);

  const processLocation = useCallback(async (latitude: number, longitude: number) => {
    try {

      const response = await axios.get(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${Config.GOOGLE_PLACES_API_KEY}`
      );
      if (response.data.results.length > 0) {
        const result = response.data.results[0];

        const parsedAddress = parseAddressComponents(
          result.address_components || [],
          result.formatted_address || ''
        );


        const locationData: LocationData = {
          address_array: parsedAddress,
          formatted_address: result.formatted_address,
          location: { latitude, longitude },
        };

        setCustomerRequestAddress(locationData);
        onLocationChange(locationData);
      } else {
        console.warn('No geocoding results found for coordinates:', { latitude, longitude });
      }
    } catch (error) {
      console.error('Error in processLocation:', error);
    } finally {
      setIsLoadingLocation(false);
    }
  }, [onLocationChange]);

  const requestCurrentLocation = useCallback(async () => {
    setIsLoadingLocation(true);
    try {
      const permission = Platform.OS === 'ios' ? PERMISSIONS.IOS.LOCATION_WHEN_IN_USE : PERMISSIONS.ANDROID.ACCESS_FINE_LOCATION;
      let status = await check(permission);
      if (status !== RESULTS.GRANTED) {
        status = await request(permission);
      }
      if (status !== RESULTS.GRANTED) {
        Alert.alert('Permission Denied', 'Location permission is required');
        setIsLoadingLocation(false);
        return;
      }


      Geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude, accuracy } = position.coords;
          console.log('Current location obtained:', { latitude, longitude, accuracy });

          if (user?.uid) {
            try {
              await VehicleService.updateCustomerLocation(user.uid, { latitude, longitude });
            } catch (error) {
              console.error('Error updating location in DB:', error);
            }
          }
          await processLocation(latitude, longitude);
        },
        (error) => {
          console.error('Geolocation error:', error);
          Alert.alert('Error', `Unable to get current location: ${error.message}`);
          setIsLoadingLocation(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 30000,
          maximumAge: 1000,
          distanceFilter: 10 
        }
      );
    } catch (error) {
      console.error('Location permission error:', error);
      setIsLoadingLocation(false);
    }
  }, [user?.uid, processLocation]);

  const parseAddressComponents = (addressComponents: any[], formattedAddress: string) => {
    const addressData = {
      address1: '',
      address2: '',
      city: '',
      state: '',
      country: '',
      zipcode: '',
    };

    if (addressComponents && addressComponents.length > 0) {
      addressComponents.forEach((component: any) => {
        const types = component.types;

        if (types.includes('street_number') || types.includes('route')) {
          if (!addressData.address1) {
            addressData.address1 = component.long_name;
          } else {
            addressData.address1 += ' ' + component.long_name;
          }
        } else if (types.includes('sublocality') || types.includes('neighborhood')) {
          addressData.address2 = component.long_name;
        } else if (types.includes('locality') || types.includes('administrative_area_level_2')) {
          addressData.city = component.long_name;
        } else if (types.includes('administrative_area_level_1')) {
          addressData.state = component.long_name;
        } else if (types.includes('country')) {
          addressData.country = component.long_name;
        } else if (types.includes('postal_code')) {
          addressData.zipcode = component.long_name;
        }
      });
    }
    if (!addressData.address1 && formattedAddress) {
      const parts = formattedAddress.split(',');
      addressData.address1 = parts[0]?.trim() || '';
      if (parts.length > 1 && !addressData.city) {
        addressData.city = parts[1]?.trim() || '';
      }
      if (parts.length > 2 && !addressData.state) {
        addressData.state = parts[2]?.trim() || '';
      }
    }

    return addressData;
  };

  const handleLocationTypeChange = (type: 'currentlocation' | 'savedlocation' | 'searchlocation') => {
    if (!radioButtonsEnabled) return; // Don't allow changes if radio buttons are disabled

    setLocationType(type);
    onLocationTypeChange?.(type);
    setCustomerRequestAddress(null);

    if (type === 'currentlocation') {
      requestCurrentLocation();
    } else {
      onLocationChange(null);
    }
  };

  const handleContainerPress = () => {
    if (!radioButtonsEnabled) {
      setRadioButtonsEnabled(true);
    }
  };


  return (
    <TouchableOpacity
      style={styles.container}
      onPress={handleContainerPress}
      activeOpacity={1}
    >
      <Text style={styles.sectionTitle}>{AppStrings.MCX_MY_LOCATION_TEXT}</Text>

      <View style={styles.radioGroup}>
        <TouchableOpacity
          style={styles.radioOption}
          onPress={() => handleLocationTypeChange('currentlocation')}
          disabled={!radioButtonsEnabled}
        >
          <View style={[
            styles.radioCircle,
            locationType === 'currentlocation' && styles.radioCircleSelected,
            !radioButtonsEnabled && styles.radioCircleDisabled
          ]} />
          <Text style={[
            styles.optionText,
            !radioButtonsEnabled && styles.optionTextDisabled
          ]}>
            {AppStrings.MCX_CHOOSE_CURRENT_LOCATION_TEXT}
          </Text>
        </TouchableOpacity>

        {showSavedLocations && (
          <TouchableOpacity
            style={styles.radioOption}
            onPress={() => handleLocationTypeChange('savedlocation')}
            disabled={!radioButtonsEnabled}
          >
            <View style={[
              styles.radioCircle,
              locationType === 'savedlocation' && styles.radioCircleSelected,
              !radioButtonsEnabled && styles.radioCircleDisabled
            ]} />
            <Text style={[
              styles.optionText,
              !radioButtonsEnabled && styles.optionTextDisabled
            ]}>
              {AppStrings.MCX_SAVED_LOCATION_TEXT}
            </Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.radioOption}
          onPress={() => handleLocationTypeChange('searchlocation')}
          disabled={!radioButtonsEnabled}
        >
          <View style={[
            styles.radioCircle,
            locationType === 'searchlocation' && styles.radioCircleSelected,
            !radioButtonsEnabled && styles.radioCircleDisabled
          ]} />
          <Text style={[
            styles.optionText,
            !radioButtonsEnabled && styles.optionTextDisabled
          ]}>
            {AppStrings.MCX_SEARCH_NEARBY_LOCATION_TEXT}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Show location display for existing data or current location or after search selection */}
      {(initialLocationData || (locationType === 'currentlocation' && customerRequestAddress) || (locationType === 'searchlocation' && customerRequestAddress)) && (
        <View style={styles.locationDisplay}>
          {isLoadingLocation ? (
            <Text style={styles.loadingText}>Loading location...</Text>
          ) : (customerRequestAddress || initialLocationData) ? (
            <Text style={styles.locationText}>
              {(customerRequestAddress || initialLocationData)?.formatted_address}
            </Text>
          ) : null}
        </View>
      )}

      {/* Saved Location Dropdown */}
      {locationType === 'savedlocation' && showSavedLocations && radioButtonsEnabled && (
        <View style={styles.dropdownContainer}>
          <Text style={styles.dropdownLabel}>{AppStrings.MCX_SELECT_SAVED_LOCATION_LABEL}</Text>
          <CommonDropdown
            data={savedLocations.map((l) => ({ label: String(l.name), value: l.id }))}
            value={selectedSavedLocation}
            onValueChange={setSelectedSavedLocation}
            placeholder="Select saved location"
          />
        </View>
      )}

    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: CommonUIParams.CUSTOM_PADDING_16,
    paddingVertical: 12,
    backgroundColor: Colors.COMMON_GREY_SHADOW_LIGHT_COLOR,
    borderRadius: 4,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_BOLD,
    color: Colors.COMMON_HEADING_COLOR_1,
    marginBottom: 12,
    fontWeight: '600',
  },
  radioGroup: {
    marginBottom: 8,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  radioCircle: {
    width: 18,
    height: 18,
    borderRadius: 9,
    borderWidth: 2,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    marginRight: 10,
    backgroundColor: 'transparent',
  },
  radioCircleSelected: {
    borderColor: Colors.PRIMARY,
    borderWidth: 6,
  },
  optionText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    fontWeight: '500',
  },
  locationDisplay: {
    backgroundColor: Colors.COMMON_WHITE_SHADE,
    borderRadius: 4,
    padding: 12,
    marginTop: 8,
    borderWidth: 1,
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
  },
  locationText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.SECONDARY,
    lineHeight: 20,
  },
  loadingText: {
    fontSize: Sizes.MEDIUM,
    fontFamily: Fonts.ROBO_REGULAR,
    color: Colors.COMMON_GREY_SHADE_LIGHT,
    fontStyle: 'italic',
  },
  dropdownContainer: {
    marginTop: 8,
  },
  dropdownLabel: {
    fontSize: Sizes.SMALL,
    fontFamily: Fonts.ROBO_BOLD,
    color: Colors.COMMON_HEADING_COLOR_1,
    marginBottom: 6,
    fontWeight: '600',
  },
  radioCircleDisabled: {
    borderColor: Colors.COMMON_GREY_SHADE_LIGHT,
    opacity: 0.5,
  },
  optionTextDisabled: {
    color: Colors.COMMON_GREY_SHADE_LIGHT,
  },
});

export default LocationSelector;