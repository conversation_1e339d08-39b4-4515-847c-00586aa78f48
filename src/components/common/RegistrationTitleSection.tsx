import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { Colors, Fonts, Sizes } from '../../utils/constants/Theme';

interface RegistrationTitleSectionProps {
  title: string;
  titleColor?: string;
  titleSize?: number;
  backgroundColor?: string;
  borderBottomWidth?: number;
  borderBottomColor?: string;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  paddingVertical?: number;
  paddingHorizontal?: number;
}

const RegistrationTitleSection: React.FC<RegistrationTitleSectionProps> = ({
  title,
  titleColor = Colors.COMMON_COMPONENT_TEXT_COLOR,
  titleSize = Sizes.LARGE,
  backgroundColor = '#FFFFFF',
  borderBottomWidth = 1,
  borderBottomColor = Colors.COMMON_GREY_SHADE_LIGHT,
  containerStyle,
  titleStyle,
  paddingVertical = 16,
  paddingHorizontal = 20,
}) => {
  return (
    <View 
      style={[
        styles.container,
        {
          backgroundColor,
          borderBottomWidth,
          borderBottomColor,
          paddingVertical,
          paddingHorizontal,
        },
        containerStyle,
      ]}
    >
      <Text 
        style={[
          styles.title,
          {
            color: titleColor,
            fontSize: titleSize,
          },
          titleStyle,
        ]}
      >
        {title}
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontFamily: Fonts.ROBO_REGULAR,
    fontWeight: '900',
    textAlign: 'center',
    fontSize: Sizes.XLARGE,
  },
});

export default RegistrationTitleSection;
