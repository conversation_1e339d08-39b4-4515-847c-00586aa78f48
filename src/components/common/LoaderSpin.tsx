// LoaderSpin.js
import React from 'react';
import {View, ActivityIndicator, StyleSheet} from 'react-native';

const LoaderSpin = () => {
  return (
    <View style={loaderSpinStyles.loaderContainer}>
      <ActivityIndicator size="large" color="#4a5c6a" />
    </View>
  );
};

const loaderSpinStyles = StyleSheet.create({
  loaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default LoaderSpin;
