import React from 'react';
import {View, Text, StyleSheet, Image, Dimensions} from 'react-native';

// Get screen width for responsive padding if needed, although not strictly necessary for this static card
// const {width} = Dimensions.get('window');

type ServicesMessageProps = {
  isIcon: boolean;
  day: string | number;
  month: string;
  services?: { service: string; subservice?: string[] }[];
  time: string; // Not used in the final status bar design but kept for completeness
  status?: string; // e.g., "Mechanic Waiting For Your Confirmation"
};

const ServicesMessage: React.FC<ServicesMessageProps> = ({
  isIcon,
  day,
  month,
  services,
  time,
  status,
}) => {
  // Determine if the component is being used for the 'upcoming' section
  const isUpcoming = !!status; 

  // We are hardcoding the fluid_change icon here as an example,
  // but in a real app, you would pass the correct icon source.
  const serviceIconSource = require('../../assets/icons/fluid_change.png'); 

  return (
    <View style={[styles.container, isUpcoming && styles.upcomingContainer]}>
      <View style={styles.contentWrapper}>
        {/* Left Section: Date */}
        <View style={styles.dateContainer}>
          <Text style={styles.day}>{day}</Text>
          <Text style={styles.month}>{month}</Text>
        </View>

        {/* Right Section: Icon, Service Details, and Status */}
        <View style={styles.rightSection}>
          <View style={styles.serviceInfo}>
            {/* Service Icon and Separator */}
            {isIcon && (
              <>
                {/* The icon in the image is a wrench and oil drop, 
                so using 'fluid_change.png' is an assumption for now. 
                You'd need the correct icon source. */}
                <Image
                  source={serviceIconSource} 
                  style={styles.serviceIcon}
                />
                <View style={styles.verticalDivider} />
              </>
            )}
            
            {/* Service Details */}
            <View style={styles.serviceDetails}>
              {services && services.length > 0 ? (
                services.map((serviceItem, index) => (
                  <View key={index}>
                    <Text style={styles.serviceTitle}>
                      {serviceItem.service || 'Unknown Service'}
                    </Text>
                    {serviceItem.subservice && serviceItem.subservice.length > 0 && (
                      <View>
                        {serviceItem.subservice.map((sub, subIndex) => (
                          <Text key={subIndex} style={styles.serviceDescription}>
                            {sub}
                          </Text>
                        ))}
                      </View>
                    )}
                  </View>
                ))
              ) : (
                <Text style={styles.serviceTitle}>No services available</Text>
              )}
            </View>
          </View>
          
          {/* Status Bar (Only appears if 'status' prop is provided) */}
          {status && (
            <View style={styles.statusBar}>
              <Text style={styles.statusText}>{status}</Text>
            </View>
          )}

          {/* Time container (for non-status appointments if needed, currently hidden by status bar logic) */}
          {!status && (
            <View style={styles.timeContainer}>
              <Text style={styles.serviceTime}>{time}</Text>
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

// ---

//## Styles for First Image Look

//The styles below implement the look and feel from the first image, including the dimensions, colors, and layout structure.

//```javascript
const styles = StyleSheet.create({
  // Main container is a wrapper around the whole appointment block
  container: {
    backgroundColor: '#fff',
   // borderRadius: 4, // Slight border radius
    overflow: 'hidden',
    marginBottom: 10,
    elevation: 2, // Shadow for Android
    shadowColor: '#000', // Shadow for iOS
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
   // shadowRadius: 1.41,
  },
  upcomingContainer: {
    // You could slightly increase the margin bottom for separation
    marginBottom: 15, 
    // Or slightly enhance the shadow
    elevation: 4, 
    shadowOpacity: 0.3,
    //shadowRadius: 4.65,
  },
  // A wrapper to place the date, service info, and status bar
  contentWrapper: {
    flexDirection: 'row',
    alignItems: 'stretch',
  },

  // The '20 Oct' block
  dateContainer: {
    backgroundColor: '#c41e3a', // Red color
    width: 70, // Fixed width based on the image's appearance
    paddingVertical: 15,
    justifyContent: 'center',
    alignItems: 'center',
  },
  day: {
    color: '#fff',
    fontSize: 28, // Slightly larger day
    fontWeight: 'bold',
  },
  month: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },

  // Container for the icon, service details, and status bar
  rightSection: {
    flex: 1, // Takes up the remaining space
    flexDirection: 'column',
  },

  // Top part of the right section: Icon + Text
  serviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 12,
    flex: 1, // Allows it to push the status bar to the bottom
  },
  serviceIcon: {
    width: 40,
    height: 40,
    marginRight: 10,
    // The icon in the image appears to be a light gray/silver, not tinted red
    tintColor: '#a9a9a9', 
    resizeMode: 'contain',
  },
  // The vertical line separating the icon from the text
  verticalDivider: {
    width: 1,
    height: '100%', // Shorter than 100% to center it visually
    backgroundColor: '#e0e0e0', // Lighter gray for a subtle line
    marginRight: 12,
  },

  serviceDetails: {
    flex: 1,
    justifyContent: 'center', // Center text vertically next to icon
  },
  serviceTitle: {
    fontSize: 14, // Larger title like "Repair"
    fontWeight: 'bold', // Bolder title
    color: '#333',
    marginBottom: 2,
  },
  serviceDescription: {
    fontSize: 12, // Slightly larger description like "Brakes"
    color: '#c41e3a', // Red color for subservice/description
    fontWeight: '500',
  },

  // Status Bar at the very bottom
  statusBar: {
    // The background is a slightly darker red, and it spans the full width of the 'rightSection'
    backgroundColor: '#a61a2e', // A darker shade of the primary red
    paddingVertical: 8,
    alignItems: 'center',
    justifyContent: 'center',
    // The bar itself appears to overlap the main content block slightly in the image, 
    // but a clean full-width bottom bar is easier to implement and looks good.
  },
  statusText: {
    color: '#fff',
    fontSize: 9,
    fontWeight: '600',
    textTransform: 'uppercase', // All caps in the image
  },

  // Fallback for non-status appointments (if needed)
  timeContainer: {
    paddingHorizontal: 12,
    paddingTop: 5,
    borderTopWidth: 1,
    borderTopColor: '#eee',
    alignItems: 'flex-end',
  },
  serviceTime: {
    fontSize: 12,
    color: '#666',
  },
});

export default ServicesMessage;