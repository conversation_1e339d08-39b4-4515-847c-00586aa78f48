import React from 'react';
import {View, Text, Image, StyleSheet} from 'react-native';
import Icon from 'react-native-vector-icons/Ionicons';

type ProfileCardProps = {
  imageUrl?: string;
  name: string;
  address: string;
  availabilityStatus: string;
  rate: number;
};

const ProfileCard: React.FC<ProfileCardProps> = ({
  imageUrl,
  name,
  address,
  availabilityStatus,
  rate,
}) => {
  const renderStars = (rating: number) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <Icon
          key={i}
          name={i <= rating ? 'star' : 'star-outline'}
          size={16}
           color="#e7b7b7ff"
        />,
      );
    }
    return stars;
  };

  return (
    <View style={styles.container}>
      <View style={styles.profileInfo}>
        <Image
          source={{uri: imageUrl || 'https://via.placeholder.com/60'}}
          style={styles.profileImage}
        />
        <View style={styles.profileText}>
          <Text style={styles.name}>{name}</Text>
          <Text style={styles.address}>{address}</Text>
        </View>
      </View>

      <View style={styles.ratingContainer}>
        <View style={styles.stars}>{renderStars(rate)}</View>
        <View style={styles.availabilityContainer}>
          <Text style={styles.availabilityLabel}>Availability: </Text>
          <Text style={styles.availabilityStatus}>{availabilityStatus}</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  profileImage: {
    width: 60,
    height: 60,
   
    marginRight: 16,
  },
  profileText: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  address: {
    fontSize: 14,
    color: '#666',
  },
  ratingContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#eee',
  },
  stars: {
    flexDirection: 'row',
  },
  availabilityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  availabilityLabel: {
    fontSize: 12,
    color: '#666',
  },
  availabilityStatus: {
    fontSize: 14,
    color: '#c41e3a',
    fontWeight: 'bold',
  },
});

export default ProfileCard;
