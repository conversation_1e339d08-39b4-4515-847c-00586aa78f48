import React, { useState } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { AppStrings } from '../../utils/constants/AppStrings';

interface CustomPermissionDialogProps {
  visible: boolean;
  onAllow: () => void;
  onAllowThisTime: () => void;
  onDeny: () => void;
  title: string;
  message: string;
  permissionType: 'location' | 'camera' | 'microphone' | 'storage';
}

const CustomPermissionDialog: React.FC<CustomPermissionDialogProps> = ({
  visible,
  onAllow,
  onAllowThisTime,
  onDeny,
  title,
}) => {
  const [selectedAccuracy, setSelectedAccuracy] = useState<'precise' | 'approximate'>('precise');

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <View style={styles.dialogContainer}>
          {/* Location Pin Icon */}
          <View style={styles.iconHeader}>
            <View style={styles.locationIconContainer}>
              <Text style={styles.locationIcon}>📍</Text>
            </View>
          </View>

          {/* Title */}
          <View style={styles.titleContainer}>
            <Text style={styles.title}>{title}</Text>
          </View>

          {/* Precision Selection */}
          <View style={styles.precisionContainer}>
            <TouchableOpacity
              style={[styles.precisionOption, selectedAccuracy === 'precise' && styles.selectedOption]}
              onPress={() => setSelectedAccuracy('precise')}
            >
              <View style={styles.precisionIconContainer}>
                <View style={styles.preciseIcon}>
                  <View style={styles.preciseIconInner}>
                    <Text style={styles.preciseIconPin}>📍</Text>
                  </View>
                </View>
              </View>
              <Text style={styles.precisionLabel}>{AppStrings.MCX_PRECISION_LABEL}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.precisionOption, selectedAccuracy === 'approximate' && styles.selectedOption]}
              onPress={() => setSelectedAccuracy('approximate')}
            >
              <View style={styles.precisionIconContainer}>
                <View style={styles.approximateIcon}>
                  <View style={styles.mapLines}>
                    <View style={[styles.mapLine, styles.mapLine1]} />
                    <View style={[styles.mapLine, styles.mapLine2]} />
                    <View style={[styles.mapLine, styles.mapLine3]} />
                    <View style={[styles.mapLine, styles.mapLine4]} />
                  </View>
                  <Text style={styles.approximateIconPin}>📍</Text>
                </View>
              </View>
              <Text style={styles.precisionLabel}>{AppStrings.MCX_APPROXIMATE_LABEL}</Text>
            </TouchableOpacity>
          </View>

          {/* Action Buttons */}
          <View style={styles.buttonContainer}>
            <TouchableOpacity style={styles.actionButton} onPress={onAllow}>
              <Text style={styles.actionButtonText}>{AppStrings.MCX_PERMISSION_1_TEXT}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={onAllowThisTime}>
              <Text style={styles.actionButtonText}>{AppStrings.MCX_PERMISSION_2_TEXT}</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.actionButton} onPress={onDeny}>
              <Text style={styles.actionButtonText}>{AppStrings.MCX_PERMISSION_3_TEXT}</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  dialogContainer: {
    backgroundColor: 'white',
    borderRadius: 28,
    width: '100%',
    maxWidth: 360,
    paddingVertical: 32,
    paddingHorizontal: 24,
    elevation: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
  },
  iconHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  locationIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#E3F2FD',
    alignItems: 'center',
    justifyContent: 'center',
  },
  locationIcon: {
    fontSize: 24,
    color: '#1976D2',
  },
  titleContainer: {
    marginBottom: 32,
  },
  title: {
    fontSize: 20,
    fontWeight: '400',
    color: '#1F1F1F',
    textAlign: 'center',
    lineHeight: 28,
  },
  precisionContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 32,
    paddingHorizontal: 8,
  },
  precisionOption: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 12,
    marginHorizontal: 8,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  selectedOption: {
    borderColor: '#1976D2',
    backgroundColor: '#F3F9FF',
  },
  precisionIconContainer: {
    marginBottom: 12,
  },
  preciseIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#E3F2FD',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: '#1976D2',
    position: 'relative',
  },
  preciseIconInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'white',
    alignItems: 'center',
    justifyContent: 'center',
  },
  preciseIconPin: {
    fontSize: 20,
    color: '#1976D2',
  },
  approximateIcon: {
    width: 80,
    height: 80,
    borderRadius: 12,
    backgroundColor: '#FFF8E1',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  mapLines: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  mapLine: {
    position: 'absolute',
    backgroundColor: '#FFC107',
    borderRadius: 2,
  },
  mapLine1: {
    width: 30,
    height: 3,
    top: 20,
    left: 15,
    transform: [{ rotate: '15deg' }],
  },
  mapLine2: {
    width: 25,
    height: 3,
    top: 35,
    left: 25,
    transform: [{ rotate: '-10deg' }],
  },
  mapLine3: {
    width: 20,
    height: 3,
    top: 50,
    left: 20,
    transform: [{ rotate: '25deg' }],
  },
  mapLine4: {
    width: 15,
    height: 3,
    top: 30,
    right: 15,
    transform: [{ rotate: '-20deg' }],
  },
  approximateIconPin: {
    fontSize: 16,
    color: '#F57C00',
    zIndex: 1,
  },
  precisionLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1F1F1F',
    textAlign: 'center',
  },
  buttonContainer: {
    gap: 8,
  },
  actionButton: {
    backgroundColor: '#E8F0FE',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 24,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1976D2',
  },
});

export default CustomPermissionDialog;
