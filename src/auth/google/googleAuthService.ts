import {useEffect, useState} from 'react';
import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import {FirebaseAuthTypes} from '@react-native-firebase/auth';
import {
  getAuth,
  onAuthStateChanged,
  signInWithCredential,
} from '@react-native-firebase/auth';
import {getApp} from '@react-native-firebase/app';

GoogleSignin.configure({
  webClientId:
    '416381233217-hmcpjknudbm78mbgcdid91ph29tukm0u.apps.googleusercontent.com',
  scopes: ['profile', 'email'],
  offlineAccess: true, // For server-side access, if needed
});

const GoogleAuthService = () => {
  const [user, setUser] = useState<FirebaseAuthTypes.User | null>(null); // Use Firebase auth.User type
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Listen for auth state changes
    const app = getApp();
    const auth = getAuth(app);
    const unsubscribe = onAuthStateChanged(
      auth,
      (firebaseUser: FirebaseAuthTypes.User | null) => {
        console.log('Firebase auth state changed:', {
          uid: firebaseUser?.uid,
          email: firebaseUser?.email,
          provider: firebaseUser?.providerData?.[0]?.providerId,
        });
        setUser(firebaseUser);
      },
    );

    // Cleanup listener on unmount
    return () => unsubscribe();
  }, []);

  const signIn = async () => {
    try {
      setLoading(true);
      setError(null);

      // Ensure previous Google session is cleared
      await GoogleSignin.signOut();

      // Perform Google Sign-In
      await GoogleSignin.hasPlayServices(); // Check Google Play Services availability
      const userInfo = await GoogleSignin.signIn();

      // Check if sign-in was cancelled
      if (!userInfo) {
        console.log('Google Sign-In was cancelled by user');
       setError('Sign-in was cancelled. Please try again if you want to continue.');
        throw new Error('SIGN_IN_CANCELLED');
      }

      // Try to get tokens and complete Firebase sign-in
      let idToken: string;
      try {
        const tokens = await GoogleSignin.getTokens();
        idToken = tokens.idToken;
      } catch (tokenError: any) {
        console.log('Failed to get tokens - user likely cancelled:', tokenError.message);
        return null;
      }
      
      if (!idToken) {
        console.log('No idToken returned from Google');
        return null;
      }

      // Create a Google credential with the idToken
      const auth = getAuth(getApp());
      const {GoogleAuthProvider} = require('@react-native-firebase/auth');
      const googleCredential = GoogleAuthProvider.credential(idToken);

      // Sign in to Firebase
      const userCredential = await signInWithCredential(auth, googleCredential);
      return userCredential.user;
    } catch (err: any) {
      console.error('Google Sign-In Error:', JSON.stringify(err, null, 2));
      
      // Check for getTokens error specifically
      if (err.message && err.message.includes('getTokens')) {
        console.log('Google Sign-In was cancelled by user during token retrieval');
        return null;
      }
      
      // Check for specific error codes
      if (err.code) {
        switch (err.code) {
          case statusCodes.SIGN_IN_CANCELLED:
            console.log('Google Sign-In was cancelled by user');
            return null;
          case statusCodes.IN_PROGRESS:
            setError('Sign-in is already in progress');
            throw new Error('Sign-in is already in progress');
          case statusCodes.PLAY_SERVICES_NOT_AVAILABLE:
            setError('Google Play Services is not available');
            throw new Error('Google Play Services is not available');
          default:
            const errorMessage = err.message || 'Google Sign-In failed';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
      } else {
        const errorMessage = err.message || 'Google Sign-In failed';
        setError(errorMessage);
        throw new Error(errorMessage);
      }
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      await GoogleSignin.signOut();
      const auth = getAuth(getApp());
      await auth.signOut();
      // setUser(null); // Handled by onAuthStateChanged
    } catch (err: any) {
      console.error('Sign out error:', JSON.stringify(err, null, 2));
      setError(err.message || 'Sign-out failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {user, loading, error, signIn, signOut};
};

export default GoogleAuthService;
