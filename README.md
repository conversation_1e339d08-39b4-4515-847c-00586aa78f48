# myCANx

This is a [**React Native**](https://reactnative.dev) project, bootstrapped using [`@react-native-community/cli`](https://github.com/react-native-community/cli).

## Requirements

- **Java Development Kit (JDK)**: **17** or higher
- **Node.js**: **18** or higher
- **Yarn** or **npm**
- **Android Studio** (for Android) or **Xcode** (for iOS)
- **CocoaPods** (for iOS, macOS only)

> **Tip:** Follow the [official environment setup guide](https://reactnative.dev/docs/environment-setup) for detailed instructions.

## Getting Started

### 1. Install dependencies

```sh
# Using npm
npm install

# OR using Yarn
yarn install
```

### 2. Start Metro (React Native bundler)

```sh
# Using npm
npm start

# OR using Yarn
yarn start
```

### 3. Run the app

#### Android

```sh
# Using npm
npm run android

# OR using Yarn
yarn android
```

#### iOS (macOS only)

First, install CocoaPods dependencies (only needed after first clone or when native deps change):

```sh
cd ios
pod install
cd ..
```

Then run:

```sh
# Using npm
npm run ios

# OR using Yarn
yarn ios
```

### 4. Modify your app

Edit `App.tsx` and save to see your changes instantly, thanks to [Fast Refresh](https://reactnative.dev/docs/fast-refresh).

---

## Troubleshooting

- Ensure you are using **Java 17+** and **Node.js 18+**.
- For more help, see the [React Native Troubleshooting guide](https://reactnative.dev/docs/troubleshooting).

## Learn More

- [React Native Docs](https://reactnative.dev/docs/getting-started)
- [React Native Blog](https://reactnative.dev/blog)
- [@facebook/react-native GitHub](https://github.com/facebook/react-native)