<manifest xmlns:android="http://schemas.android.com/apk/res/android">
  <supports-screens android:anyDensity="true" android:largeScreens="true" android:normalScreens="true" android:resizeable="true" android:smallScreens="true" android:xlargeScreens="true"/>
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.GET_ACCOUNTS"/>
  <uses-permission android:name="android.permission.USE_CREDENTIALS"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
  <uses-permission android:name="android.permission.VIBRATE" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <!--Permission for requesting Notification Access-->
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS"/>

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      android:supportsRtl="true">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustPan"
        android:screenOrientation="portrait"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        
        <!-- Branch URI Scheme -->
			  <intent-filter>
				  <!-- If utilizing $deeplink_path please explicitly declare your hosts, or utilize a wildcard(*) -->
				  <!-- REPLACE `android:scheme` with your Android URI scheme -->
				  <data android:scheme="mycanxcustomer" android:host="open" />
				  <action android:name="android.intent.action.VIEW" />
				  <category android:name="android.intent.category.DEFAULT" />
				  <category android:name="android.intent.category.BROWSABLE" />
			  </intent-filter>

        <!-- Branch App Links - Live App -->
			  <intent-filter android:autoVerify="true">
				  <action android:name="android.intent.action.VIEW" />
				  <category android:name="android.intent.category.DEFAULT" />
				  <category android:name="android.intent.category.BROWSABLE" />
				  <!-- REPLACE `android:host` with your `app.link` domain -->
				  <data android:host="p5e1.app.link" android:scheme="https"/>
          <data android:host="p5e1-alternate.app.link" android:scheme="https"/>
          <data android:host="p5e1-alternate.app.link" android:scheme="https"/>
          <data android:host="p5e1-alternate-alternate.app.link" android:scheme="https"/>      
			  </intent-filter>
      </activity>
      <meta-data android:name="com.dieam.reactnativepushnotification.notification_foreground"
        android:value="true" />
      <meta-data android:name="com.dieam.reactnativepushnotification.channel_create_default"
        android:value="true" />
      <meta-data android:name="com.dieam.reactnativepushnotification.notification_color"
        android:resource="@color/white" />
      <receiver android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationActions"
        android:exported="true" />
      <receiver
        android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationPublisher"
        android:exported="true" />
      <receiver
        android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationBootEventReceiver"
        android:exported="true">
        <intent-filter>
          <action android:name="android.intent.action.BOOT_COMPLETED" />
          <action android:name="android.intent.action.QUICKBOOT_POWERON" />
          <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
        </intent-filter>
      </receiver>

      <service
        android:name="com.dieam.reactnativepushnotification.modules.RNPushNotificationListenerService"
        android:exported="false">
        <intent-filter>
          <action android:name="com.google.firebase.MESSAGING_EVENT" />
        </intent-filter>
      </service>
      <meta-data
        android:name="com.google.firebase.messaging.default_notification_icon"
        android:resource="@drawable/ic_notification_icon" />
      <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="@string/facebook_app_id"/>
      <meta-data android:name="com.facebook.sdk.ClientToken" android:value="@string/facebook_client_token"/>
      <!-- REPLACE `BranchKey` with the value from your Branch Dashboard -->
		  <meta-data android:name="io.branch.sdk.BranchKey" android:value="key_live_egNlJk2gjh6YVRHFnY0Z0ippxqdbykYL" />
		  <!-- REPLACE `BranchKey.test` with the value from your Branch Dashboard -->
		  <meta-data android:name="io.branch.sdk.BranchKey.test" android:value="key_test_boJkVf1edb79OQLzp9Y7GbibzsloFj7z" />
		  <!-- Set to `true` to use `BranchKey.test` -->
		  <meta-data android:name="io.branch.sdk.TestMode" android:value="true" />
    </application>
</manifest>
