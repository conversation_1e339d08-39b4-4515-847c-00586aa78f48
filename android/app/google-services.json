{"project_info": {"project_number": "416381233217", "firebase_url": "https://citrus-217422.firebaseio.com", "project_id": "citrus-217422", "storage_bucket": "citrus-217422.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:416381233217:android:ea316397bdc3977d", "android_client_info": {"package_name": "com.mycanx.customer"}}, "oauth_client": [{"client_id": "416381233217-536ovoptu6i8ro3q2jo1hbv44t90f90v.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycanx.customer", "certificate_hash": "8e2f1bc8864554532c8f729b5f6d46c6b250c7c5"}}, {"client_id": "416381233217-cvh7r60jqo0ek4mr3blph2057k1ja341.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycanx.customer", "certificate_hash": "15c64784decf29a0f5cc8f8322ab478ad4e46267"}}, {"client_id": "416381233217-rn8avscp28misjoujqd6pkd9e4cbh9pa.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycanx.customer", "certificate_hash": "53d37cb717f7bfec2a2b06f4e4f53ff16dcfb5d1"}}, {"client_id": "416381233217-hmcpjknudbm78mbgcdid91ph29tukm0u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBT4AXTaYILSKMMvsiTF5Y9PecqohfrjNM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "416381233217-dahgmnmers4bgrl5gjpqsopol97qat8b.apps.googleusercontent.com", "client_type": 3}, {"client_id": "416381233217-8sirrotl416p7b9i0trp8p25nlc2pug8.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mycanx.customer"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:416381233217:android:4adb134b4cc8f6d5", "android_client_info": {"package_name": "com.mycanx.mechanic"}}, "oauth_client": [{"client_id": "416381233217-2tu6shpsgbsv1tbo3m4sea5rkmiad9kt.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycanx.mechanic", "certificate_hash": "53d37cb717f7bfec2a2b06f4e4f53ff16dcfb5d1"}}, {"client_id": "416381233217-4h93pr3idjcf96rf42s14tq80somk0lf.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycanx.mechanic", "certificate_hash": "7511322012642baf96640e3af06cd089ac4d4f75"}}, {"client_id": "416381233217-qqeivagpl8cj6e2vbu8uvueheg2tukbu.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.mycanx.mechanic", "certificate_hash": "b21df6696811ab6ac8b9bb17f882f196d9f1fdf9"}}, {"client_id": "416381233217-hmcpjknudbm78mbgcdid91ph29tukm0u.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyBT4AXTaYILSKMMvsiTF5Y9PecqohfrjNM"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "416381233217-dahgmnmers4bgrl5gjpqsopol97qat8b.apps.googleusercontent.com", "client_type": 3}, {"client_id": "416381233217-8sirrotl416p7b9i0trp8p25nlc2pug8.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mycanx.customer"}}]}}}], "configuration_version": "1"}