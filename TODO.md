# Fix Firebase Notifications on iOS

## Issue
Firebase notifications work on Android but not on iOS. The problem is that `FirebaseAppDelegateProxyEnabled` is set to `false` in Info.plist, which disables automatic Firebase handling of notifications on iOS.

## Plan
1. Update AppDelegate.swift to implement UNUserNotificationCenterDelegate
2. Add methods to register for remote notifications and handle them properly
3. Ensure proper Firebase messaging setup
4. Test the implementation

## Steps
- [x] Enable FirebaseAppDelegateProxyEnabled in Info.plist
- [x] Clean up AppDelegate.swift to use Firebase proxy
- [ ] Test notification functionality
