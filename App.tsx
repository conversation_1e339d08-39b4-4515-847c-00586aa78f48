/**
 * Author: <PERSON><PERSON><PERSON>
 * Date: 03/06/2025
 * Last Update: 03/06/2025
 *
 * @format
 */
import React, {useEffect, useRef} from 'react';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import AppNavigator from './src/navigations/AppNavigator';
import {AuthProvider} from './src/utils/configs/AuthContext';
import {StripeProvider} from '@stripe/stripe-react-native';
import Config from 'react-native-config';
import NotificationService from './src/services/NotificationService';

import {navigationRef} from './src/navigations/navigationRef';
import {Alert, BackHandler,StatusBar} from 'react-native';
import initializeFirebase from './src/utils/configs/firebaseConfig';
import FcmForeground from './src/components/message/firebaseForground';

function App(): React.ReactElement {
  const isExitingRef = useRef(false);
  const notificationServiceRef = useRef<NotificationService | null>(null);

  // Initialize notification service once
  useEffect(() => {
    notificationServiceRef.current = NotificationService.getInstance();
    // Don't cleanup navigation reference to handle background notifications
    return () => {
      // Keep navigation reference for notifications even when app closes
    };
  }, []);

  useEffect(() => {
    const backAction = () => {
      const currentRoute = navigationRef.current?.getCurrentRoute();
      const currentRouteName = currentRoute?.name;

      // If we're on the Dashboard (MainTabs), show exit dialog
      if (currentRouteName === 'MainTabs') {
        Alert.alert('Exit App', 'Do you want to exit?', [
          {
            text: 'Cancel', 
            style: 'cancel', 
            onPress: () => {
              isExitingRef.current = false;
            }
          },
          {
            text: 'Yes',
            onPress: async () => {
              try {
                isExitingRef.current = true;
                // Don't clear navigation reference - keep it for notification handling
                // if (notificationServiceRef.current) {
                //   await notificationServiceRef.current.setNavigationRef(null);
                //   // Add a small delay to ensure cleanup completes
                //   await new Promise(resolve => setTimeout(resolve, 100));
                // }
                BackHandler.exitApp();
              } catch (error) {
                console.error('Error during app exit:', error);
                BackHandler.exitApp();
              }
            }
          },
        ]);
        return true;
      }

      // For other screens, allow normal back navigation
      if (navigationRef.current?.canGoBack()) {
        navigationRef.current?.goBack();
        return true; // handled
      } else {
        // Fallback to exit dialog if can't go back
        Alert.alert('Exit App', 'Do you want to exit?', [
          {text: 'Cancel', style: 'cancel', onPress: () => null},
          {text: 'Yes', onPress: () => BackHandler.exitApp()},
        ]);
        return true; // prevent default
      }
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => {
      backHandler.remove();
      // Reset exit flag when unmounting
      isExitingRef.current = false;
    };
  }, []);

  useEffect(() => {
    const init = async () => {
      try {
        await initializeFirebase();
        // Always set navigation reference - don't clear it during exit
        if (notificationServiceRef.current) {
          await notificationServiceRef.current.setNavigationRef(navigationRef);
        }
      } catch (error) {
        console.error('Failed to initialize Firebase:', error);
      }
    };
    init();

    // Don't clear navigation reference on unmount to handle background notifications
    return () => {
      // Keep navigation reference for notifications even when app closes
    };
  }, []);

  return (
    <StripeProvider publishableKey={Config.STRIPE_TEST_API_KEY || ''}>
      <FcmForeground />
      <AuthProvider>
        <SafeAreaProvider>
          <GestureHandlerRootView style={styles.container}>
            <StatusBar
              barStyle="dark-content"
              backgroundColor="transparent"
              translucent
            />
            <AppNavigator navigationRef={navigationRef} />
          </GestureHandlerRootView>
        </SafeAreaProvider>
      </AuthProvider>
    </StripeProvider>
  );
}

const styles = {
  container: {
    flex: 1,
  },
};

export default App;
