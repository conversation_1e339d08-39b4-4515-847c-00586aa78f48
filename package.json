{"name": "myCANx", "version": "1.7.4", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@hookform/resolvers": "^5.2.2", "@invertase/react-native-apple-authentication": "^2.4.1", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.4", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/analytics": "^23.3.1", "@react-native-firebase/app": "^23.3.1", "@react-native-firebase/auth": "^23.3.1", "@react-native-firebase/database": "^23.3.1", "@react-native-firebase/messaging": "^23.3.1", "@react-native-firebase/storage": "^23.3.1", "@react-native-google-signin/google-signin": "^16.0.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/drawer": "^7.0.0", "@react-navigation/native": "^7.1.10", "@react-navigation/native-stack": "^7.3.14", "@react-navigation/stack": "^7.3.3", "@stripe/stripe-react-native": "0.39.0", "axios": "^1.12.2", "invariant": "^2.2.4", "react": "19.0.0", "react-hook-form": "^7.62.0", "react-native": "0.79.2", "react-native-branch": "^6.8.0", "react-native-config": "^1.5.9", "react-native-dotenv": "^3.4.11", "react-native-element-dropdown": "^2.12.4", "react-native-fbsdk-next": "^12", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.26.0", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.6", "react-native-google-places-textinput": "^0.8.0", "react-native-image-picker": "^8.2.1", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^14.0.0-rc.1", "react-native-permissions": "^5.4.1", "react-native-push-notification": "^8.1.1", "react-native-reanimated": "^3.19.1", "react-native-safe-area-context": "^5.4.1", "react-native-screens": "^4.16.0", "react-native-vector-icons": "^10.3.0", "react-native-webview": "^13.15.0", "yup": "^1.7.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.18.6", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "^18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.13", "@types/react": "^19.0.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.0.0", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "19.0.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}